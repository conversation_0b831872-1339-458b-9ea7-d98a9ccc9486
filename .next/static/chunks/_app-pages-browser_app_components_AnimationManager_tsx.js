"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_AnimationManager_tsx"],{

/***/ "(app-pages-browser)/./node_modules/@react-hook/latest/dist/module/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@react-hook/latest/dist/module/index.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst useLatest = current => {\n  const storedValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(current);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    storedValue.current = current;\n  });\n  return storedValue;\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (useLatest);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVhY3QtaG9vay9sYXRlc3QvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7O0FBQStCOztBQUUvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUEsK0RBQWUsU0FBUyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHJlYWN0LWhvb2svbGF0ZXN0L2Rpc3QvbW9kdWxlL2luZGV4LmpzP2MxMTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuXG5jb25zdCB1c2VMYXRlc3QgPSBjdXJyZW50ID0+IHtcbiAgY29uc3Qgc3RvcmVkVmFsdWUgPSBSZWFjdC51c2VSZWYoY3VycmVudCk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc3RvcmVkVmFsdWUuY3VycmVudCA9IGN1cnJlbnQ7XG4gIH0pO1xuICByZXR1cm4gc3RvcmVkVmFsdWU7XG59O1xuXG5leHBvcnQgZGVmYXVsdCB1c2VMYXRlc3Q7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@react-hook/latest/dist/module/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@react-hook/passive-layout-effect/dist/module/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@react-hook/passive-layout-effect/dist/module/index.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst usePassiveLayoutEffect = (react__WEBPACK_IMPORTED_MODULE_0___default())[typeof document !== 'undefined' && document.createElement !== void 0 ? 'useLayoutEffect' : 'useEffect'];\n/* harmony default export */ __webpack_exports__[\"default\"] = (usePassiveLayoutEffect);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVhY3QtaG9vay9wYXNzaXZlLWxheW91dC1lZmZlY3QvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7O0FBQTBCO0FBQzFCLCtCQUErQiw4Q0FBSztBQUNwQywrREFBZSxzQkFBc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0ByZWFjdC1ob29rL3Bhc3NpdmUtbGF5b3V0LWVmZmVjdC9kaXN0L21vZHVsZS9pbmRleC5qcz84NWMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5jb25zdCB1c2VQYXNzaXZlTGF5b3V0RWZmZWN0ID0gUmVhY3RbdHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJyAmJiBkb2N1bWVudC5jcmVhdGVFbGVtZW50ICE9PSB2b2lkIDAgPyAndXNlTGF5b3V0RWZmZWN0JyA6ICd1c2VFZmZlY3QnXTtcbmV4cG9ydCBkZWZhdWx0IHVzZVBhc3NpdmVMYXlvdXRFZmZlY3Q7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@react-hook/passive-layout-effect/dist/module/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@react-hook/resize-observer/dist/module/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-hook/resize-observer/dist/module/index.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _react_hook_passive_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-hook/passive-layout-effect */ \"(app-pages-browser)/./node_modules/@react-hook/passive-layout-effect/dist/module/index.js\");\n/* harmony import */ var _react_hook_latest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-hook/latest */ \"(app-pages-browser)/./node_modules/@react-hook/latest/dist/module/index.js\");\n/* eslint-disable no-return-assign */\n/* eslint-disable no-underscore-dangle */\n\n\n\n\n/**\n * A React hook that fires a callback whenever ResizeObserver detects a change to its size\n *\n * @param target A React ref created by `useRef()` or an HTML element\n * @param callback Invoked with a single `ResizeObserverEntry` any time\n *   the `target` resizes\n */\n\nfunction _ref() {}\nfunction useResizeObserver(target, callback, options = {}) {\n  const resizeObserver = getResizeObserver(options.polyfill);\n  const storedCallback = (0,_react_hook_latest__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(callback);\n  (0,_react_hook_passive_layout_effect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(() => {\n    let didUnsubscribe = false;\n    const targetEl = target && 'current' in target ? target.current : target;\n    if (!targetEl) return _ref;\n    function cb(entry, observer) {\n      if (didUnsubscribe) return;\n      storedCallback.current(entry, observer);\n    }\n    resizeObserver.subscribe(targetEl, cb);\n    return () => {\n      didUnsubscribe = true;\n      resizeObserver.unsubscribe(targetEl, cb);\n    };\n  }, [target, resizeObserver, storedCallback]);\n  return resizeObserver.observer;\n}\nfunction createResizeObserver(polyfill) {\n  let ticking = false;\n  let allEntries = [];\n  const callbacks = new Map();\n  const observer = new (polyfill || window.ResizeObserver)((entries, obs) => {\n    allEntries = allEntries.concat(entries);\n    function _ref2() {\n      const triggered = new Set();\n      for (let i = 0; i < allEntries.length; i++) {\n        if (triggered.has(allEntries[i].target)) continue;\n        triggered.add(allEntries[i].target);\n        const cbs = callbacks.get(allEntries[i].target);\n        cbs === null || cbs === void 0 ? void 0 : cbs.forEach(cb => cb(allEntries[i], obs));\n      }\n      allEntries = [];\n      ticking = false;\n    }\n    if (!ticking) {\n      window.requestAnimationFrame(_ref2);\n    }\n    ticking = true;\n  });\n  return {\n    observer,\n    subscribe(target, callback) {\n      var _callbacks$get;\n      observer.observe(target);\n      const cbs = (_callbacks$get = callbacks.get(target)) !== null && _callbacks$get !== void 0 ? _callbacks$get : [];\n      cbs.push(callback);\n      callbacks.set(target, cbs);\n    },\n    unsubscribe(target, callback) {\n      var _callbacks$get2;\n      const cbs = (_callbacks$get2 = callbacks.get(target)) !== null && _callbacks$get2 !== void 0 ? _callbacks$get2 : [];\n      if (cbs.length === 1) {\n        observer.unobserve(target);\n        callbacks.delete(target);\n        return;\n      }\n      const cbIndex = cbs.indexOf(callback);\n      if (cbIndex !== -1) cbs.splice(cbIndex, 1);\n      callbacks.set(target, cbs);\n    }\n  };\n}\nlet _resizeObserver;\nconst getResizeObserver = polyfill => !_resizeObserver ? _resizeObserver = createResizeObserver(polyfill) : _resizeObserver;\n/* harmony default export */ __webpack_exports__[\"default\"] = (useResizeObserver);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVhY3QtaG9vay9yZXNpemUtb2JzZXJ2ZXIvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDQTs7QUFFZ0U7QUFDckI7O0FBRTNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EseURBQXlEO0FBQ3pEO0FBQ0EseUJBQXlCLDhEQUFTO0FBQ2xDLEVBQUUsNkVBQWU7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHVCQUF1QjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtEQUFlLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHJlYWN0LWhvb2svcmVzaXplLW9ic2VydmVyL2Rpc3QvbW9kdWxlL2luZGV4LmpzPzk0M2QiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWRpc2FibGUgbm8tcmV0dXJuLWFzc2lnbiAqL1xuLyogZXNsaW50LWRpc2FibGUgbm8tdW5kZXJzY29yZS1kYW5nbGUgKi9cblxuaW1wb3J0IHVzZUxheW91dEVmZmVjdCBmcm9tICdAcmVhY3QtaG9vay9wYXNzaXZlLWxheW91dC1lZmZlY3QnO1xuaW1wb3J0IHVzZUxhdGVzdCBmcm9tICdAcmVhY3QtaG9vay9sYXRlc3QnO1xuXG4vKipcbiAqIEEgUmVhY3QgaG9vayB0aGF0IGZpcmVzIGEgY2FsbGJhY2sgd2hlbmV2ZXIgUmVzaXplT2JzZXJ2ZXIgZGV0ZWN0cyBhIGNoYW5nZSB0byBpdHMgc2l6ZVxuICpcbiAqIEBwYXJhbSB0YXJnZXQgQSBSZWFjdCByZWYgY3JlYXRlZCBieSBgdXNlUmVmKClgIG9yIGFuIEhUTUwgZWxlbWVudFxuICogQHBhcmFtIGNhbGxiYWNrIEludm9rZWQgd2l0aCBhIHNpbmdsZSBgUmVzaXplT2JzZXJ2ZXJFbnRyeWAgYW55IHRpbWVcbiAqICAgdGhlIGB0YXJnZXRgIHJlc2l6ZXNcbiAqL1xuXG5mdW5jdGlvbiBfcmVmKCkge31cbmZ1bmN0aW9uIHVzZVJlc2l6ZU9ic2VydmVyKHRhcmdldCwgY2FsbGJhY2ssIG9wdGlvbnMgPSB7fSkge1xuICBjb25zdCByZXNpemVPYnNlcnZlciA9IGdldFJlc2l6ZU9ic2VydmVyKG9wdGlvbnMucG9seWZpbGwpO1xuICBjb25zdCBzdG9yZWRDYWxsYmFjayA9IHVzZUxhdGVzdChjYWxsYmFjayk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgbGV0IGRpZFVuc3Vic2NyaWJlID0gZmFsc2U7XG4gICAgY29uc3QgdGFyZ2V0RWwgPSB0YXJnZXQgJiYgJ2N1cnJlbnQnIGluIHRhcmdldCA/IHRhcmdldC5jdXJyZW50IDogdGFyZ2V0O1xuICAgIGlmICghdGFyZ2V0RWwpIHJldHVybiBfcmVmO1xuICAgIGZ1bmN0aW9uIGNiKGVudHJ5LCBvYnNlcnZlcikge1xuICAgICAgaWYgKGRpZFVuc3Vic2NyaWJlKSByZXR1cm47XG4gICAgICBzdG9yZWRDYWxsYmFjay5jdXJyZW50KGVudHJ5LCBvYnNlcnZlcik7XG4gICAgfVxuICAgIHJlc2l6ZU9ic2VydmVyLnN1YnNjcmliZSh0YXJnZXRFbCwgY2IpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkaWRVbnN1YnNjcmliZSA9IHRydWU7XG4gICAgICByZXNpemVPYnNlcnZlci51bnN1YnNjcmliZSh0YXJnZXRFbCwgY2IpO1xuICAgIH07XG4gIH0sIFt0YXJnZXQsIHJlc2l6ZU9ic2VydmVyLCBzdG9yZWRDYWxsYmFja10pO1xuICByZXR1cm4gcmVzaXplT2JzZXJ2ZXIub2JzZXJ2ZXI7XG59XG5mdW5jdGlvbiBjcmVhdGVSZXNpemVPYnNlcnZlcihwb2x5ZmlsbCkge1xuICBsZXQgdGlja2luZyA9IGZhbHNlO1xuICBsZXQgYWxsRW50cmllcyA9IFtdO1xuICBjb25zdCBjYWxsYmFja3MgPSBuZXcgTWFwKCk7XG4gIGNvbnN0IG9ic2VydmVyID0gbmV3IChwb2x5ZmlsbCB8fCB3aW5kb3cuUmVzaXplT2JzZXJ2ZXIpKChlbnRyaWVzLCBvYnMpID0+IHtcbiAgICBhbGxFbnRyaWVzID0gYWxsRW50cmllcy5jb25jYXQoZW50cmllcyk7XG4gICAgZnVuY3Rpb24gX3JlZjIoKSB7XG4gICAgICBjb25zdCB0cmlnZ2VyZWQgPSBuZXcgU2V0KCk7XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGFsbEVudHJpZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgaWYgKHRyaWdnZXJlZC5oYXMoYWxsRW50cmllc1tpXS50YXJnZXQpKSBjb250aW51ZTtcbiAgICAgICAgdHJpZ2dlcmVkLmFkZChhbGxFbnRyaWVzW2ldLnRhcmdldCk7XG4gICAgICAgIGNvbnN0IGNicyA9IGNhbGxiYWNrcy5nZXQoYWxsRW50cmllc1tpXS50YXJnZXQpO1xuICAgICAgICBjYnMgPT09IG51bGwgfHwgY2JzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjYnMuZm9yRWFjaChjYiA9PiBjYihhbGxFbnRyaWVzW2ldLCBvYnMpKTtcbiAgICAgIH1cbiAgICAgIGFsbEVudHJpZXMgPSBbXTtcbiAgICAgIHRpY2tpbmcgPSBmYWxzZTtcbiAgICB9XG4gICAgaWYgKCF0aWNraW5nKSB7XG4gICAgICB3aW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lKF9yZWYyKTtcbiAgICB9XG4gICAgdGlja2luZyA9IHRydWU7XG4gIH0pO1xuICByZXR1cm4ge1xuICAgIG9ic2VydmVyLFxuICAgIHN1YnNjcmliZSh0YXJnZXQsIGNhbGxiYWNrKSB7XG4gICAgICB2YXIgX2NhbGxiYWNrcyRnZXQ7XG4gICAgICBvYnNlcnZlci5vYnNlcnZlKHRhcmdldCk7XG4gICAgICBjb25zdCBjYnMgPSAoX2NhbGxiYWNrcyRnZXQgPSBjYWxsYmFja3MuZ2V0KHRhcmdldCkpICE9PSBudWxsICYmIF9jYWxsYmFja3MkZ2V0ICE9PSB2b2lkIDAgPyBfY2FsbGJhY2tzJGdldCA6IFtdO1xuICAgICAgY2JzLnB1c2goY2FsbGJhY2spO1xuICAgICAgY2FsbGJhY2tzLnNldCh0YXJnZXQsIGNicyk7XG4gICAgfSxcbiAgICB1bnN1YnNjcmliZSh0YXJnZXQsIGNhbGxiYWNrKSB7XG4gICAgICB2YXIgX2NhbGxiYWNrcyRnZXQyO1xuICAgICAgY29uc3QgY2JzID0gKF9jYWxsYmFja3MkZ2V0MiA9IGNhbGxiYWNrcy5nZXQodGFyZ2V0KSkgIT09IG51bGwgJiYgX2NhbGxiYWNrcyRnZXQyICE9PSB2b2lkIDAgPyBfY2FsbGJhY2tzJGdldDIgOiBbXTtcbiAgICAgIGlmIChjYnMubGVuZ3RoID09PSAxKSB7XG4gICAgICAgIG9ic2VydmVyLnVub2JzZXJ2ZSh0YXJnZXQpO1xuICAgICAgICBjYWxsYmFja3MuZGVsZXRlKHRhcmdldCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGNiSW5kZXggPSBjYnMuaW5kZXhPZihjYWxsYmFjayk7XG4gICAgICBpZiAoY2JJbmRleCAhPT0gLTEpIGNicy5zcGxpY2UoY2JJbmRleCwgMSk7XG4gICAgICBjYWxsYmFja3Muc2V0KHRhcmdldCwgY2JzKTtcbiAgICB9XG4gIH07XG59XG5sZXQgX3Jlc2l6ZU9ic2VydmVyO1xuY29uc3QgZ2V0UmVzaXplT2JzZXJ2ZXIgPSBwb2x5ZmlsbCA9PiAhX3Jlc2l6ZU9ic2VydmVyID8gX3Jlc2l6ZU9ic2VydmVyID0gY3JlYXRlUmVzaXplT2JzZXJ2ZXIocG9seWZpbGwpIDogX3Jlc2l6ZU9ic2VydmVyO1xuZXhwb3J0IGRlZmF1bHQgdXNlUmVzaXplT2JzZXJ2ZXI7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@react-hook/resize-observer/dist/module/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/AnimationManager.tsx":
/*!*********************************************!*\
  !*** ./app/components/AnimationManager.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_hook_resize_observer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-hook/resize-observer */ \"(app-pages-browser)/./node_modules/@react-hook/resize-observer/dist/module/index.js\");\n/* harmony import */ var _Hal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Hal */ \"(app-pages-browser)/./app/components/Hal.tsx\");\n/* harmony import */ var app_utils_audioUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! app/utils/audioUtils */ \"(app-pages-browser)/./app/utils/audioUtils.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst useSize = (target)=>{\n    _s();\n    const [size, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new DOMRect());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (!(target === null || target === void 0 ? void 0 : target.current)) return;\n        setSize(target.current.getBoundingClientRect());\n    }, [\n        target\n    ]);\n    (0,_react_hook_resize_observer__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(target, (entry)=>setSize(entry.contentRect));\n    return size;\n};\n_s(useSize, \"KVvQSEXjQqR7UDaO1+CkXqNXJCg=\", false, function() {\n    return [\n        _react_hook_resize_observer__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\nconst AnimationManager = (param)=>{\n    let { agentVoiceAnalyser, userVoiceAnalyser, onOrbClick } = param;\n    _s1();\n    const canvasContainer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const size = useSize(canvasContainer);\n    const [agentVolume, setAgentVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [userVolume, setUserVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!agentVoiceAnalyser) return;\n        const dataArrayAgent = new Uint8Array(agentVoiceAnalyser.frequencyBinCount);\n        const getVolume = ()=>{\n            setAgentVolume((0,app_utils_audioUtils__WEBPACK_IMPORTED_MODULE_3__.normalizeVolume)(agentVoiceAnalyser, dataArrayAgent, 48));\n            requestAnimationFrame(getVolume);\n        };\n        getVolume();\n    }, [\n        agentVoiceAnalyser\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!userVoiceAnalyser) return;\n        const dataArray = new Uint8Array(userVoiceAnalyser.frequencyBinCount);\n        const getVolume = ()=>{\n            setUserVolume((0,app_utils_audioUtils__WEBPACK_IMPORTED_MODULE_3__.normalizeVolume)(userVoiceAnalyser, dataArray, 48));\n            requestAnimationFrame(getVolume);\n        };\n        getVolume();\n    }, [\n        userVoiceAnalyser\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            ref: canvasContainer,\n            onClick: onOrbClick,\n            className: \"orb-animation\",\n            children: canvasContainer.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Hal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                width: size.width,\n                height: size.height,\n                agentVolume: agentVolume,\n                userVolume: userVolume\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/components/AnimationManager.tsx\",\n                lineNumber: 66,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/components/AnimationManager.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/components/AnimationManager.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AnimationManager, \"S2uHD8taFYNGUUCzAcyDhFLFj58=\", false, function() {\n    return [\n        useSize\n    ];\n});\n_c = AnimationManager;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnimationManager);\nvar _c;\n$RefreshReg$(_c, \"AnimationManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21wb25lbnRzL0FuaW1hdGlvbk1hbmFnZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQU9lO0FBQzZDO0FBQ3BDO0FBQytCO0FBRXZELE1BQU1RLFVBQVUsQ0FBQ0M7O0lBQ2YsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdULCtDQUFRQSxDQUFVLElBQUlVO0lBRTlDWCxzREFBZUEsQ0FBQztRQUNkLElBQUksRUFBQ1EsbUJBQUFBLDZCQUFBQSxPQUFRSSxPQUFPLEdBQUU7UUFDdEJGLFFBQVFGLE9BQU9JLE9BQU8sQ0FBQ0MscUJBQXFCO0lBQzlDLEdBQUc7UUFBQ0w7S0FBTztJQUVYSix1RUFBaUJBLENBQUNJLFFBQVEsQ0FBQ00sUUFBVUosUUFBUUksTUFBTUMsV0FBVztJQUM5RCxPQUFPTjtBQUNUO0dBVk1GOztRQVFKSCxtRUFBaUJBOzs7QUFVbkIsTUFBTVksbUJBQThCO1FBQUMsRUFDbkNDLGtCQUFrQixFQUNsQkMsaUJBQWlCLEVBQ2pCQyxVQUFVLEVBQ0o7O0lBQ04sTUFBTUMsa0JBQWtCakIsNkNBQU1BLENBQW9CO0lBQ2xELE1BQU1NLE9BQU9GLFFBQVFhO0lBRXJCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHckIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDc0IsWUFBWUMsY0FBYyxHQUFHdkIsK0NBQVFBLENBQUM7SUFFN0NDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDZSxvQkFBb0I7UUFDekIsTUFBTVEsaUJBQWlCLElBQUlDLFdBQVdULG1CQUFtQlUsaUJBQWlCO1FBQzFFLE1BQU1DLFlBQVk7WUFDaEJOLGVBQWVoQixxRUFBZUEsQ0FBQ1csb0JBQW9CUSxnQkFBZ0I7WUFDbkVJLHNCQUFzQkQ7UUFDeEI7UUFDQUE7SUFDRixHQUFHO1FBQUNYO0tBQW1CO0lBRXZCZixnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ2dCLG1CQUFtQjtRQUN4QixNQUFNWSxZQUFZLElBQUlKLFdBQVdSLGtCQUFrQlMsaUJBQWlCO1FBQ3BFLE1BQU1DLFlBQVk7WUFDaEJKLGNBQWNsQixxRUFBZUEsQ0FBQ1ksbUJBQW1CWSxXQUFXO1lBQzVERCxzQkFBc0JEO1FBQ3hCO1FBQ0FBO0lBQ0YsR0FBRztRQUFDVjtLQUFrQjtJQUV0QixxQkFDRSw4REFBQ2E7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0M7WUFBT0MsS0FBS2Q7WUFBaUJlLFNBQVNoQjtZQUFZYSxXQUFVO3NCQUMxRFosZ0JBQWdCUixPQUFPLGtCQUN0Qiw4REFBQ1AsNENBQUdBO2dCQUNGK0IsT0FBTzNCLEtBQUsyQixLQUFLO2dCQUNqQkMsUUFBUTVCLEtBQUs0QixNQUFNO2dCQUNuQmhCLGFBQWFBO2dCQUNiRSxZQUFZQTs7Ozs7Ozs7Ozs7Ozs7OztBQU14QjtJQTdDTVA7O1FBTVNUOzs7S0FOVFM7QUErQ04sK0RBQWVBLGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvY29tcG9uZW50cy9BbmltYXRpb25NYW5hZ2VyLnRzeD9iMjI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwge1xuICB1c2VMYXlvdXRFZmZlY3QsXG4gIHVzZVN0YXRlLFxuICB1c2VFZmZlY3QsXG4gIHVzZVJlZixcbiAgdHlwZSBGQyxcbiAgdHlwZSBSZWZPYmplY3QsXG59IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHVzZVJlc2l6ZU9ic2VydmVyIGZyb20gXCJAcmVhY3QtaG9vay9yZXNpemUtb2JzZXJ2ZXJcIjtcbmltcG9ydCBIYWwgZnJvbSBcIi4vSGFsXCI7XG5pbXBvcnQgeyBub3JtYWxpemVWb2x1bWUgfSBmcm9tIFwiYXBwL3V0aWxzL2F1ZGlvVXRpbHNcIjtcblxuY29uc3QgdXNlU2l6ZSA9ICh0YXJnZXQ6IFJlZk9iamVjdDxIVE1MQnV0dG9uRWxlbWVudD4gfCBudWxsKSA9PiB7XG4gIGNvbnN0IFtzaXplLCBzZXRTaXplXSA9IHVzZVN0YXRlPERPTVJlY3Q+KG5ldyBET01SZWN0KCkpO1xuXG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCF0YXJnZXQ/LmN1cnJlbnQpIHJldHVybjtcbiAgICBzZXRTaXplKHRhcmdldC5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpKTtcbiAgfSwgW3RhcmdldF0pO1xuXG4gIHVzZVJlc2l6ZU9ic2VydmVyKHRhcmdldCwgKGVudHJ5KSA9PiBzZXRTaXplKGVudHJ5LmNvbnRlbnRSZWN0KSk7XG4gIHJldHVybiBzaXplO1xufTtcblxuaW50ZXJmYWNlIFByb3BzIHtcbiAgYWdlbnRWb2ljZUFuYWx5c2VyPzogQW5hbHlzZXJOb2RlO1xuICB1c2VyVm9pY2VBbmFseXNlcj86IEFuYWx5c2VyTm9kZTtcbiAgb25PcmJDbGljazogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgQW5pbWF0aW9uTWFuYWdlcjogRkM8UHJvcHM+ID0gKHtcbiAgYWdlbnRWb2ljZUFuYWx5c2VyLFxuICB1c2VyVm9pY2VBbmFseXNlcixcbiAgb25PcmJDbGljayxcbn06IFByb3BzKSA9PiB7XG4gIGNvbnN0IGNhbnZhc0NvbnRhaW5lciA9IHVzZVJlZjxIVE1MQnV0dG9uRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IHNpemUgPSB1c2VTaXplKGNhbnZhc0NvbnRhaW5lcik7XG5cbiAgY29uc3QgW2FnZW50Vm9sdW1lLCBzZXRBZ2VudFZvbHVtZV0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW3VzZXJWb2x1bWUsIHNldFVzZXJWb2x1bWVdID0gdXNlU3RhdGUoMCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWFnZW50Vm9pY2VBbmFseXNlcikgcmV0dXJuO1xuICAgIGNvbnN0IGRhdGFBcnJheUFnZW50ID0gbmV3IFVpbnQ4QXJyYXkoYWdlbnRWb2ljZUFuYWx5c2VyLmZyZXF1ZW5jeUJpbkNvdW50KTtcbiAgICBjb25zdCBnZXRWb2x1bWUgPSAoKSA9PiB7XG4gICAgICBzZXRBZ2VudFZvbHVtZShub3JtYWxpemVWb2x1bWUoYWdlbnRWb2ljZUFuYWx5c2VyLCBkYXRhQXJyYXlBZ2VudCwgNDgpKTtcbiAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZShnZXRWb2x1bWUpO1xuICAgIH07XG4gICAgZ2V0Vm9sdW1lKCk7XG4gIH0sIFthZ2VudFZvaWNlQW5hbHlzZXJdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghdXNlclZvaWNlQW5hbHlzZXIpIHJldHVybjtcbiAgICBjb25zdCBkYXRhQXJyYXkgPSBuZXcgVWludDhBcnJheSh1c2VyVm9pY2VBbmFseXNlci5mcmVxdWVuY3lCaW5Db3VudCk7XG4gICAgY29uc3QgZ2V0Vm9sdW1lID0gKCkgPT4ge1xuICAgICAgc2V0VXNlclZvbHVtZShub3JtYWxpemVWb2x1bWUodXNlclZvaWNlQW5hbHlzZXIsIGRhdGFBcnJheSwgNDgpKTtcbiAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZShnZXRWb2x1bWUpO1xuICAgIH07XG4gICAgZ2V0Vm9sdW1lKCk7XG4gIH0sIFt1c2VyVm9pY2VBbmFseXNlcl0pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgPGJ1dHRvbiByZWY9e2NhbnZhc0NvbnRhaW5lcn0gb25DbGljaz17b25PcmJDbGlja30gY2xhc3NOYW1lPVwib3JiLWFuaW1hdGlvblwiPlxuICAgICAgICB7Y2FudmFzQ29udGFpbmVyLmN1cnJlbnQgJiYgKFxuICAgICAgICAgIDxIYWxcbiAgICAgICAgICAgIHdpZHRoPXtzaXplLndpZHRofVxuICAgICAgICAgICAgaGVpZ2h0PXtzaXplLmhlaWdodH1cbiAgICAgICAgICAgIGFnZW50Vm9sdW1lPXthZ2VudFZvbHVtZX1cbiAgICAgICAgICAgIHVzZXJWb2x1bWU9e3VzZXJWb2x1bWV9XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQW5pbWF0aW9uTWFuYWdlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUxheW91dEVmZmVjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlUmVzaXplT2JzZXJ2ZXIiLCJIYWwiLCJub3JtYWxpemVWb2x1bWUiLCJ1c2VTaXplIiwidGFyZ2V0Iiwic2l6ZSIsInNldFNpemUiLCJET01SZWN0IiwiY3VycmVudCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsImVudHJ5IiwiY29udGVudFJlY3QiLCJBbmltYXRpb25NYW5hZ2VyIiwiYWdlbnRWb2ljZUFuYWx5c2VyIiwidXNlclZvaWNlQW5hbHlzZXIiLCJvbk9yYkNsaWNrIiwiY2FudmFzQ29udGFpbmVyIiwiYWdlbnRWb2x1bWUiLCJzZXRBZ2VudFZvbHVtZSIsInVzZXJWb2x1bWUiLCJzZXRVc2VyVm9sdW1lIiwiZGF0YUFycmF5QWdlbnQiLCJVaW50OEFycmF5IiwiZnJlcXVlbmN5QmluQ291bnQiLCJnZXRWb2x1bWUiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJkYXRhQXJyYXkiLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJyZWYiLCJvbkNsaWNrIiwid2lkdGgiLCJoZWlnaHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/AnimationManager.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/Hal.tsx":
/*!********************************!*\
  !*** ./app/components/Hal.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/VoiceBotContextProvider */ \"(app-pages-browser)/./app/context/VoiceBotContextProvider.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst PULSE_PERIOD_SECONDS = 3;\nconst PULSE_SIZE_MULTIPLIER = 1.02;\nconst AVERAGE_ROTATION_PERIOD_SECONDS = 6;\nconst ROCKING_SWING_FRACTION = 18;\nconst ROCKING_PERIOD_SECONDS = 6;\nconst SLEEP_SPEED_MULTIPLIER = 0.5;\nconst DEFLATE_TRANSITION_TIME_MS = 1000;\nconst DEFLATE_PULL = 1.3;\nconst INFLATE_TRANSITION_TIME_MS = 300;\nconst FOCUS_TRANSITION_TIME_MS = 700;\nconst RELAX_TRANSITION_TIME_MS = 1000;\nconst CHATTER_SIZE_MULTIPLIER = 1.15;\nconst CHATTER_WINDOW_SIZE = 3;\nconst FOCUS_SPEED_MULTIPLIER = 5;\nconst FOCUS_SIZE_MULTIPLIER = 0.5;\nconst pi = (n)=>Math.PI * n;\nconst coordsFrom = (param, distance, angle)=>{\n    let { x, y } = param;\n    return {\n        x: x + distance * Math.cos(angle),\n        y: y + distance * Math.sin(angle)\n    };\n};\nconst bezier = (ctx, cp1, cp2, end)=>{\n    ctx.bezierCurveTo(cp1.x, cp1.y, cp2.x, cp2.y, end.x, end.y);\n};\nconst lerp = (start, stop, amt)=>amt * (stop - start) + start;\n/**\n * https://easings.net/#easeInOutQuad\n */ const easeInOutQuad = (x)=>x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2;\nconst getCenter = (ctx)=>{\n    const { width, height } = ctx.canvas.getBoundingClientRect();\n    return {\n        x: width / 2,\n        y: height / 2\n    };\n};\nconst crescent = (ctx, offset, radius, deflation, strokeStyle)=>{\n    /**\n   * to approximate a circle segment, the two control points of a bezier curve\n   * need to be at a specific distance, represented by\n   *\n   * circleRadius * (4 / 3) * Math.tan(Math.PI / (2 * n))\n   *\n   * where n is # of segments in a full circle. the angle for that distance is\n   * simply \"tangential to the arc at the closest endpoint\"\n   */ const bezierDistance = radius * (4 / 3) * Math.tan(pi(1 / 8));\n    const trueCenter = getCenter(ctx);\n    const center = {\n        x: trueCenter.x * (1 + offset.x),\n        y: trueCenter.y * (1 + offset.y)\n    };\n    ctx.strokeStyle = strokeStyle;\n    ctx.beginPath();\n    // the \"true circle\" part\n    const arcStart = deflation.angle + pi(1 / 2);\n    const arcEnd = deflation.angle + pi(3 / 2);\n    ctx.arc(center.x, center.y, radius, arcStart, arcEnd, false);\n    // the \"deflatable\" part. two bezier curves each approximating a quarter-circle\n    const start = coordsFrom(center, radius, arcEnd);\n    const midpointPull = radius * deflation.depth * DEFLATE_PULL;\n    const mid = coordsFrom(center, radius - midpointPull, lerp(deflation.angle, pi(3) - deflation.angle, deflation.depth));\n    const end = coordsFrom(center, radius, arcStart);\n    /**\n   * The way to find a control point is to take that distance from the equation\n   * above, and move \"tangential to the circle at the closer endpoint\"\n   */ const bez1 = {\n        cp1: coordsFrom(start, bezierDistance, arcEnd + pi(1 / 2)),\n        cp2: coordsFrom(mid, bezierDistance, deflation.angle + pi(3 / 2))\n    };\n    const bez2 = {\n        cp1: coordsFrom(mid, bezierDistance, deflation.angle + pi(1 / 2)),\n        cp2: coordsFrom(end, bezierDistance, arcStart + pi(3 / 2))\n    };\n    bezier(ctx, bez1.cp1, bez1.cp2, mid);\n    bezier(ctx, bez2.cp1, bez2.cp2, end);\n    ctx.stroke();\n};\nconst makeGradient = (ctx, offset, angle, parts)=>{\n    const center = getCenter(ctx);\n    const x1 = center.x * (1 - Math.cos(angle) + offset.x);\n    const y1 = center.y * (1 - Math.sin(angle) + offset.y);\n    const x2 = center.x * (1 + Math.cos(angle) + offset.x);\n    const y2 = center.y * (1 + Math.sin(angle) + offset.y);\n    const g = ctx.createLinearGradient(x1, y1, x2, y2);\n    parts.forEach((param)=>{\n        let { pct, color } = param;\n        g.addColorStop(pct, color);\n    });\n    return g;\n};\nvar Color;\n(function(Color) {\n    Color[\"springGreen\"] = \"#13ef93cc\";\n    Color[\"springGreenLight\"] = \"#b8f8d2cc\";\n    Color[\"eucalyptus\"] = \"#027a48cc\";\n    Color[\"rose\"] = \"#f185becc\";\n    Color[\"lavender\"] = \"#ba80f5cc\";\n    Color[\"chryslerBlue\"] = \"#3a00d3cc\";\n    Color[\"azure\"] = \"#149afbcc\";\n    Color[\"transparent\"] = \"transparent\";\n})(Color || (Color = {}));\n/**\n * These were picked from a bakeoff of some random color configs\n */ const lines = [\n    {\n        segments: [\n            {\n                pct: 0.42,\n                color: \"transparent\"\n            },\n            {\n                pct: 0.61,\n                color: \"#f185becc\"\n            }\n        ],\n        startAngle: 3.52,\n        speedMultiplier: 1.21,\n        centerOffset: {\n            x: 0.01,\n            y: -0.01\n        },\n        radiusOffset: 0.02,\n        width: 3.38\n    },\n    {\n        segments: [\n            {\n                pct: 0.28,\n                color: \"#13ef93cc\"\n            },\n            {\n                pct: 0.62,\n                color: \"#f185becc\"\n            }\n        ],\n        startAngle: 1.59,\n        speedMultiplier: 0.64,\n        centerOffset: {\n            x: -0.03,\n            y: -0.01\n        },\n        radiusOffset: 0.05,\n        width: 2.39\n    },\n    {\n        segments: [\n            {\n                pct: 0.31,\n                color: \"#027a48cc\"\n            },\n            {\n                pct: 0.66,\n                color: \"#3a00d3cc\"\n            }\n        ],\n        startAngle: 2.86,\n        speedMultiplier: 0.94,\n        centerOffset: {\n            x: 0.02,\n            y: 0.02\n        },\n        radiusOffset: -0.06,\n        width: 2.64\n    },\n    {\n        segments: [\n            {\n                pct: 0.16,\n                color: \"#3a00d3cc\"\n            },\n            {\n                pct: 0.62,\n                color: \"#027a48cc\"\n            },\n            {\n                pct: 0.75,\n                color: \"#ba80f5cc\"\n            }\n        ],\n        startAngle: 0.65,\n        speedMultiplier: 1.23,\n        centerOffset: {\n            x: 0.01,\n            y: 0.0\n        },\n        radiusOffset: -0.01,\n        width: 2.32\n    },\n    {\n        segments: [\n            {\n                pct: 0.02,\n                color: \"#13ef93cc\"\n            },\n            {\n                pct: 0.8,\n                color: \"#149afbcc\"\n            }\n        ],\n        startAngle: 6.19,\n        speedMultiplier: 1.18,\n        centerOffset: {\n            x: -0.04,\n            y: 0.02\n        },\n        radiusOffset: 0.01,\n        width: 3.98\n    },\n    {\n        segments: [\n            {\n                pct: 0.2,\n                color: \"transparent\"\n            },\n            {\n                pct: 0.47,\n                color: \"transparent\"\n            },\n            {\n                pct: 0.81,\n                color: \"#b8f8d2cc\"\n            }\n        ],\n        startAngle: 0.49,\n        speedMultiplier: 0.51,\n        centerOffset: {\n            x: 0.04,\n            y: -0.01\n        },\n        radiusOffset: -0.04,\n        width: 1.19\n    }\n];\nconst LINE_COUNT = lines.length;\nconst radiusOscillation = (shape)=>1 + (PULSE_SIZE_MULTIPLIER - 1) * Math.sin(shape.current.time * pi(1) / PULSE_PERIOD_SECONDS / 1000) * lerp(1, 0, shape.current.deflation) * lerp(1, 0.33, shape.current.focus);\nconst rollingAverage = (noise, start)=>{\n    const noiseWindow = noise.slice(start, start + CHATTER_WINDOW_SIZE);\n    return noiseWindow.reduce((a, b)=>a + b) / noiseWindow.length;\n};\nconst speechSimulation = (shape, start)=>lerp(1, CHATTER_SIZE_MULTIPLIER, rollingAverage(shape.current.agentNoise, start));\nconst listeningSimulation = (shape, start)=>lerp(1, 1 / CHATTER_SIZE_MULTIPLIER, rollingAverage(shape.current.userNoise, start));\nconst draw = (ctx, shape, last, now)=>{\n    shape.current.time += (now - last) * lerp(1, FOCUS_SPEED_MULTIPLIER, shape.current.focus) * lerp(1, SLEEP_SPEED_MULTIPLIER, shape.current.deflation);\n    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n    ctx.filter = \"saturate(150%)\";\n    const center = getCenter(ctx);\n    const maxRadius = Math.min(center.x, center.y);\n    lines.forEach((line, i)=>{\n        ctx.lineWidth = line.width;\n        ctx.shadowColor = line.segments[0].color;\n        ctx.shadowBlur = line.width * 1.1;\n        const radius = maxRadius * 0.8 * speechSimulation(shape, i) * listeningSimulation(shape, i) * radiusOscillation(shape);\n        const gradient = makeGradient(ctx, line.centerOffset, line.startAngle + shape.current.time * pi(1) / 1000 / AVERAGE_ROTATION_PERIOD_SECONDS * line.speedMultiplier, line.segments);\n        crescent(ctx, line.centerOffset, (radius + line.radiusOffset * radius) * lerp(1, FOCUS_SIZE_MULTIPLIER, easeInOutQuad(shape.current.focus)), {\n            depth: easeInOutQuad(shape.current.deflation),\n            angle: pi(3 / 2) + pi(Math.sin(shape.current.time * pi(1) / (ROCKING_PERIOD_SECONDS * SLEEP_SPEED_MULTIPLIER) / 1000) / ROCKING_SWING_FRACTION)\n        }, gradient);\n    });\n    requestAnimationFrame((t)=>{\n        draw(ctx, shape, now, t);\n    });\n};\nconst deflationDepth = (orbState)=>{\n    switch(orbState){\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.LISTENING:\n            return 0;\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.THINKING:\n            return 0;\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.NONE:\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.SLEEPING:\n            return 1;\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.SPEAKING:\n            return 0;\n        default:\n            return 0;\n    }\n};\nconst focusIntensity = (orbState)=>{\n    switch(orbState){\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.LISTENING:\n            return 0;\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.THINKING:\n            return 1;\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.SLEEPING:\n            return 0;\n        case _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.SPEAKING:\n            return 0;\n        default:\n            return 0;\n    }\n};\nconst transition = function(generation, orbState, shape, last) {\n    let now = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : last;\n    // drop this transition if a newer one has been produced\n    if (shape.current.generation > generation) return;\n    const depth = deflationDepth(orbState);\n    if (depth < shape.current.deflation) {\n        const step = (now - last) / INFLATE_TRANSITION_TIME_MS;\n        shape.current.deflation = Math.max(depth, shape.current.deflation - step);\n    } else {\n        const step = (now - last) / DEFLATE_TRANSITION_TIME_MS;\n        shape.current.deflation = Math.min(depth, shape.current.deflation + step);\n    }\n    const focus = focusIntensity(orbState);\n    if (focus < shape.current.focus) {\n        const step = (now - last) / RELAX_TRANSITION_TIME_MS;\n        shape.current.focus = Math.max(focus, shape.current.focus - step);\n    } else {\n        const step = (now - last) / FOCUS_TRANSITION_TIME_MS;\n        shape.current.focus = Math.min(focus, shape.current.focus + step);\n    }\n    if (shape.current.deflation !== depth || shape.current.focus !== focus) {\n        requestAnimationFrame((ts)=>{\n            transition(generation, orbState, shape, now, ts);\n        });\n    }\n};\nconst Hal = (param)=>{\n    let { width = 0, height = 0, agentVolume = 0, userVolume = 0 } = param;\n    _s();\n    const { status: orbState } = (0,_context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.useVoiceBot)();\n    const canvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const shape = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        generation: 0,\n        time: 0,\n        deflation: deflationDepth(orbState),\n        focus: focusIntensity(orbState),\n        agentNoise: Array(LINE_COUNT + CHATTER_WINDOW_SIZE).fill(agentVolume),\n        userNoise: Array(LINE_COUNT + CHATTER_WINDOW_SIZE).fill(orbState === _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.SLEEPING ? 0 : userVolume)\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (canvas.current) {\n            const context = canvas.current.getContext(\"2d\");\n            if (context) {\n                const now = performance.now();\n                requestAnimationFrame((t)=>{\n                    draw(context, shape, now, t);\n                });\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        shape.current.generation += 1;\n        requestAnimationFrame((t)=>{\n            transition(shape.current.generation, orbState, shape, t);\n        });\n    }, [\n        orbState\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        shape.current.agentNoise.shift();\n        shape.current.agentNoise.push(agentVolume);\n    }, [\n        agentVolume\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (orbState === _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.VoiceBotStatus.SLEEPING) return;\n        shape.current.userNoise.shift();\n        shape.current.userNoise.push(userVolume);\n    }, [\n        userVolume,\n        orbState\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvas,\n        width: width,\n        height: height\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/components/Hal.tsx\",\n        lineNumber: 459,\n        columnNumber: 10\n    }, undefined);\n};\n_s(Hal, \"ORFjAUyoHLst5be9CIf8AcWbv6U=\", false, function() {\n    return [\n        _context_VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_2__.useVoiceBot\n    ];\n});\n_c = Hal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Hal);\nvar _c;\n$RefreshReg$(_c, \"Hal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Hal.tsx\n"));

/***/ })

}]);