/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcomponents%2FAnimatedBackground.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FDeepgramContextProvider.js%22%2C%22ids%22%3A%5B%22DeepgramContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FMicrophoneContextProvider.js%22%2C%22ids%22%3A%5B%22MicrophoneContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FVoiceBotContextProvider.tsx%22%2C%22ids%22%3A%5B%22VoiceBotProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fira%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FABCFavorit-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-favorit%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22favorit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcomponents%2FAnimatedBackground.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FDeepgramContextProvider.js%22%2C%22ids%22%3A%5B%22DeepgramContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FMicrophoneContextProvider.js%22%2C%22ids%22%3A%5B%22MicrophoneContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FVoiceBotContextProvider.tsx%22%2C%22ids%22%3A%5B%22VoiceBotProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fira%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FABCFavorit-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-favorit%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22favorit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/AnimatedBackground.js */ \"(app-pages-browser)/./app/components/AnimatedBackground.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/DeepgramContextProvider.js */ \"(app-pages-browser)/./app/context/DeepgramContextProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/MicrophoneContextProvider.js */ \"(app-pages-browser)/./app/context/MicrophoneContextProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/VoiceBotContextProvider.tsx */ \"(app-pages-browser)/./app/context/VoiceBotContextProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"fallback\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"fallback\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira\",\"display\":\"fallback\"}],\"variableName\":\"fira\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Fira_Code\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-fira\\\",\\\"display\\\":\\\"fallback\\\"}],\\\"variableName\\\":\\\"fira\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/local/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/ABCFavorit-Bold.woff2\",\"weight\":\"700\",\"variable\":\"--font-favorit\",\"display\":\"fallback\"}],\"variableName\":\"favorit\"} */ \"(app-pages-browser)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/ABCFavorit-Bold.woff2\\\",\\\"weight\\\":\\\"700\\\",\\\"variable\\\":\\\"--font-favorit\\\",\\\"display\\\":\\\"fallback\\\"}],\\\"variableName\\\":\\\"favorit\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcomponents%2FAnimatedBackground.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FDeepgramContextProvider.js%22%2C%22ids%22%3A%5B%22DeepgramContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FMicrophoneContextProvider.js%22%2C%22ids%22%3A%5B%22MicrophoneContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FVoiceBotContextProvider.tsx%22%2C%22ids%22%3A%5B%22VoiceBotProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fira%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FABCFavorit-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-favorit%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22favorit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/api/app-dynamic.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcz8wNTg5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuLi9zaGFyZWQvbGliL2FwcC1keW5hbWljXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLWR5bmFtaWMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={592:(e,r,t)=>{var n=t(722);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},722:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(592);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"5a12a5927718\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2E4ODYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1YTEyYTU5Mjc3MThcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/AnimatedBackground.js":
/*!**********************************************!*\
  !*** ./app/components/AnimatedBackground.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _utils_deepgramUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/deepgramUtils */ \"(app-pages-browser)/./app/utils/deepgramUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Player = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_lottiefiles_react-lottie-player_dist_lottie-react_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! @lottiefiles/react-lottie-player */ \"(app-pages-browser)/./node_modules/@lottiefiles/react-lottie-player/dist/lottie-react.esm.js\")).then((mod)=>mod.Player), {\n    loadableGenerated: {\n        modules: [\n            \"app/components/AnimatedBackground.js -> \" + \"@lottiefiles/react-lottie-player\"\n        ]\n    },\n    ssr: false\n});\n_c = Player;\nconst AnimatedBackground = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Player, {\n                autoplay: true,\n                loop: true,\n                src: (0,_utils_deepgramUtils__WEBPACK_IMPORTED_MODULE_3__.withBasePath)(\"/sts-bg.json\"),\n                rendererSettings: {\n                    preserveAspectRatio: \"xMidYMid slice\"\n                },\n                className: \"animatedBackground\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/components/AnimatedBackground.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"relative\",\n                    zIndex: 1,\n                    backgroundColor: \"black\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/components/AnimatedBackground.js\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/components/AnimatedBackground.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = AnimatedBackground;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnimatedBackground);\nvar _c, _c1;\n$RefreshReg$(_c, \"Player\");\n$RefreshReg$(_c1, \"AnimatedBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/AnimatedBackground.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/context/DeepgramContextProvider.js":
/*!************************************************!*\
  !*** ./app/context/DeepgramContextProvider.js ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeepgramContextProvider: function() { return /* binding */ DeepgramContextProvider; },\n/* harmony export */   useDeepgram: function() { return /* binding */ useDeepgram; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var app_utils_deepgramUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! app/utils/deepgramUtils */ \"(app-pages-browser)/./app/utils/deepgramUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ DeepgramContextProvider,useDeepgram auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst DeepgramContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst DeepgramContextProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [socketState, setSocketState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [reconnectAttempts, setReconnectAttempts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rateLimited, setRateLimited] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const keepAlive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const maxReconnectAttempts = 5;\n    const connectToDeepgram = async ()=>{\n        if (reconnectAttempts >= maxReconnectAttempts) {\n            console.log(\"Max reconnect attempts reached.\");\n            // we don't actually know this is a rate limit, but want to show this anyways\n            setRateLimited(true);\n            return;\n        }\n        setSocketState(0); // connecting\n        const newSocket = new WebSocket(\"wss://agent.deepgram.com/v1/agent/converse\", [\n            \"bearer\",\n            await (0,app_utils_deepgramUtils__WEBPACK_IMPORTED_MODULE_2__.getAuthToken)()\n        ]);\n        const onOpen = ()=>{\n            setSocketState(1); // connected\n            setReconnectAttempts(0); // reset reconnect attempts after a successful connection\n            console.log(\"WebSocket connected.\");\n            keepAlive.current = setInterval((0,app_utils_deepgramUtils__WEBPACK_IMPORTED_MODULE_2__.sendKeepAliveMessage)(newSocket), 10000);\n        };\n        const onError = (err)=>{\n            setSocketState(2); // error\n            console.error(\"Websocket error\", err);\n        };\n        const onClose = ()=>{\n            clearInterval(keepAlive.current);\n            setSocketState(3); // closed\n            console.info(\"WebSocket closed. Attempting to reconnect...\");\n            setTimeout(connectToDeepgram, 3000); // reconnect after 3 seconds\n            setReconnectAttempts((attempts)=>attempts + 1);\n        };\n        const onMessage = ()=>{\n        // console.info(\"message\", e);\n        };\n        newSocket.binaryType = \"arraybuffer\";\n        newSocket.addEventListener(\"open\", onOpen);\n        newSocket.addEventListener(\"error\", onError);\n        newSocket.addEventListener(\"close\", onClose);\n        newSocket.addEventListener(\"message\", onMessage);\n        setSocket(newSocket);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeepgramContext.Provider, {\n        value: {\n            socket,\n            socketState,\n            rateLimited,\n            connectToDeepgram\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/context/DeepgramContextProvider.js\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DeepgramContextProvider, \"jMYqmjuuwPTeCWcZ7iTsRKxVebI=\");\n_c = DeepgramContextProvider;\nfunction useDeepgram() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DeepgramContext);\n}\n_s1(useDeepgram, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n\nvar _c;\n$RefreshReg$(_c, \"DeepgramContextProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/context/DeepgramContextProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/context/MicrophoneContextProvider.js":
/*!**************************************************!*\
  !*** ./app/context/MicrophoneContextProvider.js ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MicrophoneContextProvider: function() { return /* binding */ MicrophoneContextProvider; },\n/* harmony export */   useMicrophone: function() { return /* binding */ useMicrophone; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MicrophoneContextProvider,useMicrophone auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst MicrophoneContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst MicrophoneContextProvider = (param)=>{\n    let { children } = param;\n    _s();\n    /**\n   * Possible microphone states:\n   * - not setup - null\n   * - setting up - 0\n   * - ready - 1\n   * - open - 2\n   * - paused - 3\n   */ const [microphoneState, setMicrophoneState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [microphone, setMicrophone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [microphoneAudioContext, setMicrophoneAudioContext] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [processor, setProcessor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const setupMicrophone = async ()=>{\n        setMicrophoneState(0);\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: 16000,\n                    channelCount: 1,\n                    volume: 1.0,\n                    echoCancellation: true,\n                    noiseSuppression: false,\n                    latency: 0\n                }\n            });\n            const microphoneAudioContext = new AudioContext();\n            const microphone = microphoneAudioContext.createMediaStreamSource(stream);\n            const processor = microphoneAudioContext.createScriptProcessor(4096, 1, 1);\n            setMicrophone(microphone);\n            setMicrophoneAudioContext(microphoneAudioContext);\n            setProcessor(processor);\n            setMicrophoneState(1);\n        } catch (err) {\n            console.error(err);\n            if (err.name !== \"NotFoundError\" && err.name !== \"NotAllowedError\") {\n                console.log(err.name);\n            }\n        }\n    };\n    const startMicrophone = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        microphone.connect(processor);\n        processor.connect(microphoneAudioContext.destination);\n        setMicrophoneState(2);\n    }, [\n        processor,\n        microphoneAudioContext,\n        microphone\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MicrophoneContext.Provider, {\n        value: {\n            microphone,\n            startMicrophone,\n            // stopMicrophone,\n            setupMicrophone,\n            microphoneState,\n            microphoneAudioContext,\n            setMicrophoneAudioContext,\n            processor\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/context/MicrophoneContextProvider.js\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MicrophoneContextProvider, \"5cW/LARo41e9zi+Hoh4X+7Fq8f8=\");\n_c = MicrophoneContextProvider;\nfunction useMicrophone() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MicrophoneContext);\n}\n_s1(useMicrophone, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n\nvar _c;\n$RefreshReg$(_c, \"MicrophoneContextProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/context/MicrophoneContextProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/context/VoiceBotContextProvider.tsx":
/*!*************************************************!*\
  !*** ./app/context/VoiceBotContextProvider.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventType: function() { return /* binding */ EventType; },\n/* harmony export */   VoiceBotContext: function() { return /* binding */ VoiceBotContext; },\n/* harmony export */   VoiceBotProvider: function() { return /* binding */ VoiceBotProvider; },\n/* harmony export */   VoiceBotStatus: function() { return /* binding */ VoiceBotStatus; },\n/* harmony export */   isAssistantMessage: function() { return /* binding */ isAssistantMessage; },\n/* harmony export */   isConversationMessage: function() { return /* binding */ isConversationMessage; },\n/* harmony export */   isLatencyMessage: function() { return /* binding */ isLatencyMessage; },\n/* harmony export */   isUserMessage: function() { return /* binding */ isUserMessage; },\n/* harmony export */   useVoiceBot: function() { return /* binding */ useVoiceBot; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./VoiceBotReducer */ \"(app-pages-browser)/./app/context/VoiceBotReducer.ts\");\n/* __next_internal_client_entry_do_not_use__ EventType,isConversationMessage,isLatencyMessage,isUserMessage,isAssistantMessage,VoiceBotStatus,VoiceBotContext,useVoiceBot,VoiceBotProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst defaultSleepTimeoutSeconds = 30;\nvar EventType;\n(function(EventType) {\n    EventType[\"SETTINGS_APPLIED\"] = \"SettingsApplied\";\n    EventType[\"AGENT_AUDIO_DONE\"] = \"AgentAudioDone\";\n    EventType[\"USER_STARTED_SPEAKING\"] = \"UserStartedSpeaking\";\n    EventType[\"AGENT_STARTED_SPEAKING\"] = \"AgentStartedSpeaking\";\n    EventType[\"CONVERSATION_TEXT\"] = \"ConversationText\";\n    EventType[\"END_OF_THOUGHT\"] = \"EndOfThought\";\n})(EventType || (EventType = {}));\nconst isConversationMessage = (voiceBotMessage)=>isUserMessage(voiceBotMessage) || isAssistantMessage(voiceBotMessage);\nconst isLatencyMessage = (voiceBotMessage)=>voiceBotMessage.tts_latency !== undefined;\nconst isUserMessage = (conversationMessage)=>conversationMessage.user !== undefined;\nconst isAssistantMessage = (conversationMessage)=>conversationMessage.assistant !== undefined;\nvar VoiceBotStatus;\n(function(VoiceBotStatus) {\n    VoiceBotStatus[\"LISTENING\"] = \"listening\";\n    VoiceBotStatus[\"THINKING\"] = \"thinking\";\n    VoiceBotStatus[\"SPEAKING\"] = \"speaking\";\n    VoiceBotStatus[\"SLEEPING\"] = \"sleeping\";\n    VoiceBotStatus[\"NONE\"] = \"\";\n})(VoiceBotStatus || (VoiceBotStatus = {}));\nconst initialState = {\n    status: \"\",\n    sleepTimer: 0,\n    messages: [],\n    attachParamsToCopyUrl: true,\n    behindTheScenesEvents: []\n};\nconst VoiceBotContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useVoiceBot() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(VoiceBotContext);\n    if (!context) throw new Error(\"useVoiceBot must be used within a VoiceBotProvider\");\n    return context;\n}\n_s(useVoiceBot, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction VoiceBotProvider(param) {\n    let { children } = param;\n    _s1();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(_VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.voiceBotReducer, initialState);\n    // Note: After waking from sleep, the bot must wait for the user to speak before playing audio.\n    // This prevents unintended audio playback and conversation queue logging if the user rapidly toggles between\n    // sleep and wake states in the middle of a bot response.\n    const isWaitingForUserVoiceAfterSleep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            dispatch({\n                type: _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.INCREMENT_SLEEP_TIMER\n            });\n        }, 1000);\n        return ()=>clearInterval(interval);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (state.sleepTimer > defaultSleepTimeoutSeconds) {\n            startSleeping();\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        state.sleepTimer\n    ]);\n    const addVoicebotMessage = (newMessage)=>{\n        dispatch({\n            type: _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.ADD_MESSAGE,\n            payload: newMessage\n        });\n    };\n    const addBehindTheScenesEvent = (event)=>{\n        dispatch({\n            type: _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.ADD_BEHIND_SCENES_EVENT,\n            payload: event\n        });\n    };\n    const startSpeaking = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        let wakeFromSleep = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (wakeFromSleep || state.status !== \"sleeping\") {\n            dispatch({\n                type: _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.START_SPEAKING\n            });\n        }\n    }, [\n        state.status\n    ]);\n    const startListening = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        let wakeFromSleep = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (wakeFromSleep || state.status !== \"sleeping\") {\n            dispatch({\n                type: _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.START_LISTENING\n            });\n        }\n    }, [\n        state.status\n    ]);\n    const startSleeping = ()=>{\n        isWaitingForUserVoiceAfterSleep.current = true;\n        dispatch({\n            type: _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.START_SLEEPING\n        });\n    };\n    const toggleSleep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (state.status === \"sleeping\") {\n            startListening(true);\n        } else {\n            startSleeping();\n        }\n    }, [\n        state.status,\n        startListening\n    ]);\n    const endOfTurn = (message, previousMessage)=>isAssistantMessage(previousMessage) && isUserMessage(message);\n    const displayOrder = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const conv = state.messages.filter(isConversationMessage);\n        const lat = state.messages.filter(isLatencyMessage);\n        const acc = [];\n        conv.forEach((conversationMessage, i, arr)=>{\n            const previousMessage = arr[i - 1];\n            if (previousMessage && endOfTurn(conversationMessage, previousMessage)) {\n                const latencyMessage = lat.shift();\n                if (latencyMessage) acc.push(latencyMessage);\n            }\n            acc.push(conversationMessage);\n            if (isAssistantMessage(conversationMessage) && i === arr.length - 1) {\n                const latencyMessage = lat.shift();\n                if (latencyMessage) acc.push(latencyMessage);\n            }\n        });\n        return acc;\n    }, [\n        state.messages\n    ]);\n    const setAttachParamsToCopyUrl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((attachParamsToCopyUrl)=>{\n        dispatch({\n            type: _VoiceBotReducer__WEBPACK_IMPORTED_MODULE_2__.SET_PARAMS_ON_COPY_URL,\n            payload: attachParamsToCopyUrl\n        });\n    }, []);\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            ...state,\n            isWaitingForUserVoiceAfterSleep,\n            displayOrder,\n            addVoicebotMessage,\n            addBehindTheScenesEvent,\n            startSpeaking,\n            startListening,\n            startSleeping,\n            toggleSleep,\n            setAttachParamsToCopyUrl\n        }), [\n        state,\n        startListening,\n        startSpeaking,\n        toggleSleep,\n        setAttachParamsToCopyUrl,\n        displayOrder\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VoiceBotContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/deepgram-voice-agent/app/context/VoiceBotContextProvider.tsx\",\n        lineNumber: 233,\n        columnNumber: 10\n    }, this);\n}\n_s1(VoiceBotProvider, \"uUKNuq5wUJMXZ71ltrg5wzryeBM=\");\n_c = VoiceBotProvider;\nvar _c;\n$RefreshReg$(_c, \"VoiceBotProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/context/VoiceBotContextProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/context/VoiceBotReducer.ts":
/*!****************************************!*\
  !*** ./app/context/VoiceBotReducer.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_BEHIND_SCENES_EVENT: function() { return /* binding */ ADD_BEHIND_SCENES_EVENT; },\n/* harmony export */   ADD_MESSAGE: function() { return /* binding */ ADD_MESSAGE; },\n/* harmony export */   INCREMENT_SLEEP_TIMER: function() { return /* binding */ INCREMENT_SLEEP_TIMER; },\n/* harmony export */   SET_PARAMS_ON_COPY_URL: function() { return /* binding */ SET_PARAMS_ON_COPY_URL; },\n/* harmony export */   START_LISTENING: function() { return /* binding */ START_LISTENING; },\n/* harmony export */   START_SLEEPING: function() { return /* binding */ START_SLEEPING; },\n/* harmony export */   START_SPEAKING: function() { return /* binding */ START_SPEAKING; },\n/* harmony export */   START_THINKING: function() { return /* binding */ START_THINKING; },\n/* harmony export */   voiceBotReducer: function() { return /* binding */ voiceBotReducer; }\n/* harmony export */ });\n/* harmony import */ var _VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./VoiceBotContextProvider */ \"(app-pages-browser)/./app/context/VoiceBotContextProvider.tsx\");\n\nconst START_LISTENING = \"start_listening\";\nconst START_THINKING = \"start_thinking\";\nconst START_SPEAKING = \"start_speaking\";\nconst START_SLEEPING = \"start_sleeping\";\nconst INCREMENT_SLEEP_TIMER = \"increment_sleep_timer\";\nconst ADD_MESSAGE = \"add_message\";\nconst SET_PARAMS_ON_COPY_URL = \"set_attach_params_to_copy_url\";\nconst ADD_BEHIND_SCENES_EVENT = \"add_behind_scenes_event\";\nconst voiceBotReducer = (state, action)=>{\n    switch(action.type){\n        case START_LISTENING:\n            return {\n                ...state,\n                status: _VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_0__.VoiceBotStatus.LISTENING,\n                sleepTimer: 0\n            };\n        case START_THINKING:\n            return {\n                ...state,\n                status: _VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_0__.VoiceBotStatus.THINKING\n            };\n        case START_SPEAKING:\n            return {\n                ...state,\n                status: _VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_0__.VoiceBotStatus.SPEAKING,\n                sleepTimer: 0\n            };\n        case START_SLEEPING:\n            return {\n                ...state,\n                status: _VoiceBotContextProvider__WEBPACK_IMPORTED_MODULE_0__.VoiceBotStatus.SLEEPING\n            };\n        case INCREMENT_SLEEP_TIMER:\n            return {\n                ...state,\n                sleepTimer: state.sleepTimer + 1\n            };\n        case ADD_MESSAGE:\n            return {\n                ...state,\n                messages: [\n                    ...state.messages,\n                    action.payload\n                ]\n            };\n        case SET_PARAMS_ON_COPY_URL:\n            return {\n                ...state,\n                attachParamsToCopyUrl: action.payload\n            };\n        case ADD_BEHIND_SCENES_EVENT:\n            return {\n                ...state,\n                behindTheScenesEvents: [\n                    ...state.behindTheScenesEvents,\n                    action.payload\n                ]\n            };\n        default:\n            return state;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/context/VoiceBotReducer.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/utils/audioUtils.js":
/*!*********************************!*\
  !*** ./app/utils/audioUtils.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertFloat32ToInt16: function() { return /* binding */ convertFloat32ToInt16; },\n/* harmony export */   createAudioBuffer: function() { return /* binding */ createAudioBuffer; },\n/* harmony export */   downsample: function() { return /* binding */ downsample; },\n/* harmony export */   normalizeVolume: function() { return /* binding */ normalizeVolume; },\n/* harmony export */   playAudioBuffer: function() { return /* binding */ playAudioBuffer; }\n/* harmony export */ });\n// Audio processing utilities\nfunction createAudioBuffer(audioContext, data) {\n    let sampleRate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 24000;\n    const audioDataView = new Int16Array(data);\n    if (audioDataView.length === 0) {\n        console.error(\"Received audio data is empty.\");\n        return;\n    }\n    const buffer = audioContext.createBuffer(1, audioDataView.length, sampleRate);\n    const channelData = buffer.getChannelData(0);\n    // Convert linear16 PCM to float [-1, 1]\n    for(let i = 0; i < audioDataView.length; i++){\n        channelData[i] = audioDataView[i] / 32768;\n    }\n    return buffer;\n}\nfunction playAudioBuffer(audioContext, buffer, startTimeRef, analyzer) {\n    const source = audioContext.createBufferSource();\n    source.buffer = buffer;\n    source.connect(analyzer); // Connect to the analyzer\n    analyzer.connect(audioContext.destination); // Connect the analyzer to the destination\n    const currentTime = audioContext.currentTime;\n    if (startTimeRef.current < currentTime) {\n        startTimeRef.current = currentTime;\n    }\n    source.start(startTimeRef.current);\n    startTimeRef.current += buffer.duration;\n    return source; // Return the source if you need to further manipulate it (stop, pause, etc.)\n}\nfunction downsample(buffer, fromSampleRate, toSampleRate) {\n    if (fromSampleRate === toSampleRate) {\n        return buffer;\n    }\n    const sampleRateRatio = fromSampleRate / toSampleRate;\n    const newLength = Math.round(buffer.length / sampleRateRatio);\n    const result = new Float32Array(newLength);\n    let offsetResult = 0;\n    let offsetBuffer = 0;\n    while(offsetResult < result.length){\n        const nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio);\n        let accum = 0, count = 0;\n        for(let i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++){\n            accum += buffer[i];\n            count++;\n        }\n        result[offsetResult] = accum / count;\n        offsetResult++;\n        offsetBuffer = nextOffsetBuffer;\n    }\n    return result;\n}\nfunction convertFloat32ToInt16(buffer) {\n    let l = buffer.length;\n    const buf = new Int16Array(l);\n    while(l--){\n        buf[l] = Math.min(1, buffer[l]) * 0x7fff;\n    }\n    return buf.buffer;\n}\nconst normalizeVolume = (analyzer, dataArray, normalizationFactor)=>{\n    analyzer.getByteFrequencyData(dataArray);\n    const sum = dataArray.reduce((acc, val)=>acc + val, 0);\n    const average = sum / dataArray.length;\n    return Math.min(average / normalizationFactor, 1);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/utils/audioUtils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/utils/deepgramUtils.ts":
/*!************************************!*\
  !*** ./app/utils/deepgramUtils.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthToken: function() { return /* binding */ getAuthToken; },\n/* harmony export */   sendKeepAliveMessage: function() { return /* binding */ sendKeepAliveMessage; },\n/* harmony export */   sendMicToSocket: function() { return /* binding */ sendMicToSocket; },\n/* harmony export */   sendSocketMessage: function() { return /* binding */ sendSocketMessage; },\n/* harmony export */   withBasePath: function() { return /* binding */ withBasePath; }\n/* harmony export */ });\n/* harmony import */ var _utils_audioUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/audioUtils */ \"(app-pages-browser)/./app/utils/audioUtils.js\");\n/* harmony import */ var next_config_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next.config.mjs */ \"(app-pages-browser)/./next.config.mjs\");\n\n\nconst getAuthToken = async ()=>{\n    const result = await (await fetch(withBasePath(\"/api/authenticate\"), {\n        method: \"POST\"\n    })).json();\n    return result.access_token;\n};\nconst sendMicToSocket = (socket)=>(event)=>{\n        var _event_inputBuffer;\n        const inputData = event === null || event === void 0 ? void 0 : (_event_inputBuffer = event.inputBuffer) === null || _event_inputBuffer === void 0 ? void 0 : _event_inputBuffer.getChannelData(0);\n        const downsampledData = (0,_utils_audioUtils__WEBPACK_IMPORTED_MODULE_0__.downsample)(inputData, 48000, 16000);\n        const audioDataToSend = (0,_utils_audioUtils__WEBPACK_IMPORTED_MODULE_0__.convertFloat32ToInt16)(downsampledData);\n        socket.send(audioDataToSend);\n    };\nconst sendSocketMessage = (socket, message)=>{\n    socket.send(JSON.stringify(message));\n};\nconst sendKeepAliveMessage = (socket)=>()=>{\n        sendSocketMessage(socket, {\n            type: \"KeepAlive\"\n        });\n    };\nconst withBasePath = (path)=>{\n    const basePath = next_config_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].basePath || \"/\";\n    if (path === \"/\") return basePath;\n    return basePath + path;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/utils/deepgramUtils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hcHAtZHluYW1pYy5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQWlDQTs7O2VBQXdCQTs7Ozs7NEVBakNOOytFQUNHO0FBZ0NOLFNBQVNBLFFBQ3RCQyxjQUE2QyxFQUM3Q0MsT0FBMkI7UUFtQ2hCQztJQWpDWCxJQUFJQyxrQkFBc0M7UUFDeEMsd0RBQXdEO1FBQ3hEQyxTQUFTLENBQUFDO2dCQUFDLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUUsR0FBQUg7WUFDdkMsSUFBSSxDQUFDRyxXQUFXLE9BQU87WUFDdkIsSUFBSUMsSUFBeUIsRUFBYztnQkFDekMsSUFBSUYsV0FBVztvQkFDYixPQUFPO2dCQUNUO2dCQUNBLElBQUlELE9BQU87b0JBQ1QsT0FDRSxXQURGLEdBQ0UsSUFBQUksWUFBQUMsSUFBQSxFQUFDQyxLQUFBQTs7NEJBQ0VOLE1BQU1PLE9BQU87MENBQ2QsSUFBQUgsWUFBQUksR0FBQSxFQUFDQyxNQUFBQSxDQUFBQTs0QkFDQVQsTUFBTVUsS0FBSzs7O2dCQUdsQjtZQUNGO1lBQ0EsT0FBTztRQUNUO0lBQ0Y7SUFFQSxJQUFJLE9BQU9oQixtQkFBbUIsWUFBWTtRQUN4Q0csZ0JBQWdCYyxNQUFNLEdBQUdqQjtJQUMzQjtJQUVBLE1BQU1FLGdCQUFnQjtRQUNwQixHQUFHQyxlQUFlO1FBQ2xCLEdBQUdGLE9BQU87SUFDWjtJQUVBLE9BQU9pQixDQUFBQSxHQUFBQSxVQUFBQSxPQUFRLEVBQUM7UUFDZCxHQUFHaEIsYUFBYTtRQUNoQmlCLFNBQU8sQ0FBRWpCLG1DQUFBQSxjQUFja0IsaUJBQWlCLHFCQUEvQmxCLGlDQUFpQ2lCLE9BQU87SUFDbkQ7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvYXBwLWR5bmFtaWMudHN4P2M1NjUiXSwibmFtZXMiOlsiZHluYW1pYyIsImR5bmFtaWNPcHRpb25zIiwib3B0aW9ucyIsIm1lcmdlZE9wdGlvbnMiLCJsb2FkYWJsZU9wdGlvbnMiLCJsb2FkaW5nIiwicGFyYW0iLCJlcnJvciIsImlzTG9hZGluZyIsInBhc3REZWxheSIsInByb2Nlc3MiLCJfanN4cnVudGltZSIsImpzeHMiLCJwIiwibWVzc2FnZSIsImpzeCIsImJyIiwic3RhY2siLCJsb2FkZXIiLCJMb2FkYWJsZSIsIm1vZHVsZXMiLCJsb2FkYWJsZUdlbmVyYXRlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBY08sTUFBQUEsZ0JBQXNCQyxtQkFBQUEsQ0FBdUM7U0FBdkNDLGFBQVVDLEtBQVE7SUFDN0MsSUFBSSxFQUFBQyxNQUFPQyxFQUFBQSxRQUFXLEtBQUFDO1FBQ3BCLE9BQU1ELFdBQUlFLGFBQUFBO1FBQ1osVUFBQVAsY0FBQU8saUJBQUEsQ0FBQUg7SUFFQTtJQUNGLE9BQUFEOztLQU42QkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL2xhenktZHluYW1pYy9keW5hbWljLWJhaWxvdXQtdG8tY3NyLnRzeD80ZmY0Il0sIm5hbWVzIjpbIl9iYWlsb3V0dG9jc3IiLCJyZXF1aXJlIiwiQmFpbG91dFRvQ1NSIiwiY2hpbGRyZW4iLCJyZWFzb24iLCJ3aW5kb3ciLCJwYXJhbSIsIkJhaWxvdXRUb0NTUkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadcss = __webpack_require__(/*! ./preload-css */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { $$typeof, default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && \"default\" in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                typeof window === \"undefined\" ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadcss.PreloadCss, {\n                    moduleIds: opts.modules\n                }) : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js ***!
  \***********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadCss\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadCss;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _requestasyncstorageexternal = __webpack_require__(/*! ../../../client/components/request-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/request-async-storage.external.js\");\nfunction PreloadCss(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (typeof window !== \"undefined\") {\n        return null;\n    }\n    const requestStore = (0, _requestasyncstorageexternal.getExpectedRequestStore)(\"next/dynamic css\");\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (requestStore.reactLoadableManifest && moduleIds) {\n        const manifest = requestStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const cssFiles = manifest[key].files.filter((file)=>file.endsWith(\".css\"));\n            allFiles.push(...cssFiles);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((file)=>{\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                // @ts-ignore\n                precedence: \"dynamic\",\n                rel: \"stylesheet\",\n                href: requestStore.assetPrefix + \"/_next/\" + encodeURI(file),\n                as: \"style\"\n            }, file);\n        })\n    });\n} //# sourceMappingURL=preload-css.js.map\n_c = PreloadCss;\nvar _c;\n$RefreshReg$(_c, \"PreloadCss\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvcHJlbG9hZC1jc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTyxNQUFBQSwrQkFBc0VDLG1CQUFBQSxDQUFBO1NBQWxEQyxXQUFXQyxLQUFYO0lBQ3pCLE1BQUFDLFNBQUEsS0FBQUQ7SUFDQSwrRUFBbUM7UUFDakMsT0FBT0UsV0FBQTtRQUNUO0lBRUE7SUFDQSxNQUFNQyxlQUFhLElBQUFOLDZCQUFBTyx1QkFBQTtJQUVuQixNQUFBRCxXQUFBO0lBQ0EsNEVBQWtEO0lBQ2xELGtEQUEwQ0Y7UUFDeENJLGFBQU1DLHFCQUF3QkMsSUFBQUEsV0FBQUE7UUFDOUIsTUFBS0QsV0FBTUUsYUFBa0JELHFCQUFBO2FBQzNCLE1BQUtELE9BQVNFLFVBQU07WUFDcEIsS0FBQUYsUUFBTUcsQ0FBQUEsSUFBV0gsRUFBQUE7WUFHakJILE1BQUFBLFdBQWlCTSxRQUFBQSxDQUFBQSxJQUFBQSxDQUFBQSxLQUFBQSxDQUFBQSxNQUFBQSxDQUFBQSxDQUFBQSxPQUFBQSxLQUFBQSxRQUFBQSxDQUFBQTtZQUNuQk4sU0FBQU8sSUFBQSxJQUFBRDtRQUNGO0lBRUE7UUFDRU4sU0FBT1EsTUFBQTtRQUNUO0lBRUE7V0FFS1IsV0FBQUEsR0FBQUEsQ0FBQUEsR0FBU1MsWUFBS0MsR0FBQUEsRUFBQUEsWUFBQUEsUUFBQUEsRUFBQUE7a0JBQ2JWLFNBQUFTLEdBQUEsRUFBQUM7bUJBR2lCLGtCQUFBQyxZQUFBQyxHQUFBO2dCQUNiQyxhQUFZO2dCQUNaQyxZQUFJO2dCQUNKQyxLQUFBQTtnQkFDQUMsTUFBR2QsYUFBQWUsV0FBQSxlQUFBQyxVQUFBUjtnQkFMRUEsSUFBQUE7WUFRWCxHQUFBQTs7SUFHTjs7S0ExQzJCZCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvbGF6eS1keW5hbWljL3ByZWxvYWQtY3NzLnRzeD9kZTVjIl0sIm5hbWVzIjpbIl9yZXF1ZXN0YXN5bmNzdG9yYWdlZXh0ZXJuYWwiLCJyZXF1aXJlIiwiUHJlbG9hZENzcyIsInBhcmFtIiwibW9kdWxlSWRzIiwid2luZG93IiwiYWxsRmlsZXMiLCJnZXRFeHBlY3RlZFJlcXVlc3RTdG9yZSIsInJlcXVlc3RTdG9yZSIsIm1hbmlmZXN0IiwicmVhY3RMb2FkYWJsZU1hbmlmZXN0Iiwia2V5IiwiY3NzRmlsZXMiLCJwdXNoIiwibGVuZ3RoIiwibWFwIiwiZmlsZSIsIl9qc3hydW50aW1lIiwianN4IiwicHJlY2VkZW5jZSIsInJlbCIsImhyZWYiLCJhcyIsImFzc2V0UHJlZml4IiwiZW5jb2RlVVJJIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira\",\"display\":\"fallback\"}],\"variableName\":\"fira\"}":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Fira_Code","arguments":[{"subsets":["latin"],"variable":"--font-fira","display":"fallback"}],"variableName":"fira"} ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Fira_Code_8f1fa9', '__Fira_Code_Fallback_8f1fa9'\",\"fontStyle\":\"normal\"},\"className\":\"__className_8f1fa9\",\"variable\":\"__variable_8f1fa9\"};\n    if(true) {\n      // 1753640941255\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/agent/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwL2xheW91dC50c3hcIixcImltcG9ydFwiOlwiRmlyYV9Db2RlXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdLFwidmFyaWFibGVcIjpcIi0tZm9udC1maXJhXCIsXCJkaXNwbGF5XCI6XCJmYWxsYmFja1wifV0sXCJ2YXJpYWJsZU5hbWVcIjpcImZpcmFcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyx3RkFBd0Y7QUFDbkgsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQW1KLGNBQWMsNERBQTREO0FBQzNQLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZm9udC9nb29nbGUvdGFyZ2V0LmNzcz81MDdjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidfX0ZpcmFfQ29kZV84ZjFmYTknLCAnX19GaXJhX0NvZGVfRmFsbGJhY2tfOGYxZmE5J1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lXzhmMWZhOVwiLFwidmFyaWFibGVcIjpcIl9fdmFyaWFibGVfOGYxZmE5XCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTM2NDA5NDEyNTVcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiL1VzZXJzL2hvdG92by9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9kZWVwZ3JhbS12b2ljZS1hZ2VudC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9hZ2VudC9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira\",\"display\":\"fallback\"}],\"variableName\":\"fira\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"fallback\"}],\"variableName\":\"inter\"}":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-inter","display":"fallback"}],"variableName":"inter"} ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_170341', '__Inter_Fallback_170341'\",\"fontStyle\":\"normal\"},\"className\":\"__className_170341\",\"variable\":\"__variable_170341\"};\n    if(true) {\n      // 1753640941255\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/agent/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwL2xheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl0sXCJ2YXJpYWJsZVwiOlwiLS1mb250LWludGVyXCIsXCJkaXNwbGF5XCI6XCJmYWxsYmFja1wifV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsZ0ZBQWdGO0FBQzNHLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFtSixjQUFjLDREQUE0RDtBQUMzUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/MTk0OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInX19JbnRlcl8xNzAzNDEnLCAnX19JbnRlcl9GYWxsYmFja18xNzAzNDEnXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfMTcwMzQxXCIsXCJ2YXJpYWJsZVwiOlwiX192YXJpYWJsZV8xNzAzNDFcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1MzY0MDk0MTI1NVxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCIvVXNlcnMvaG90b3ZvL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL2RlZXBncmFtLXZvaWNlLWFnZW50L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL2FnZW50L19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"fallback\"}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/ABCFavorit-Bold.woff2\",\"weight\":\"700\",\"variable\":\"--font-favorit\",\"display\":\"fallback\"}],\"variableName\":\"favorit\"}":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/local/target.css?{"path":"app/layout.tsx","import":"","arguments":[{"src":"./fonts/ABCFavorit-Bold.woff2","weight":"700","variable":"--font-favorit","display":"fallback"}],"variableName":"favorit"} ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__favorit_526b21', '__favorit_Fallback_526b21'\",\"fontWeight\":700},\"className\":\"__className_526b21\",\"variable\":\"__variable_526b21\"};\n    if(true) {\n      // 1753640941256\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/agent/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvbG9jYWwvdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJhcHAvbGF5b3V0LnRzeFwiLFwiaW1wb3J0XCI6XCJcIixcImFyZ3VtZW50c1wiOlt7XCJzcmNcIjpcIi4vZm9udHMvQUJDRmF2b3JpdC1Cb2xkLndvZmYyXCIsXCJ3ZWlnaHRcIjpcIjcwMFwiLFwidmFyaWFibGVcIjpcIi0tZm9udC1mYXZvcml0XCIsXCJkaXNwbGF5XCI6XCJmYWxsYmFja1wifV0sXCJ2YXJpYWJsZU5hbWVcIjpcImZhdm9yaXRcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyxnRkFBZ0Y7QUFDM0csT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQW1KLGNBQWMsNERBQTREO0FBQzNQLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZm9udC9sb2NhbC90YXJnZXQuY3NzPzlhNzQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ19fZmF2b3JpdF81MjZiMjEnLCAnX19mYXZvcml0X0ZhbGxiYWNrXzUyNmIyMSdcIixcImZvbnRXZWlnaHRcIjo3MDB9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV81MjZiMjFcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlXzUyNmIyMVwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUzNjQwOTQxMjU2XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIi9Vc2Vycy9ob3Rvdm8vRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvZGVlcGdyYW0tdm9pY2UtYWdlbnQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvYWdlbnQvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/ABCFavorit-Bold.woff2\",\"weight\":\"700\",\"variable\":\"--font-favorit\",\"display\":\"fallback\"}],\"variableName\":\"favorit\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/MzNjZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./next.config.mjs":
/*!*************************!*\
  !*** ./next.config.mjs ***!
  \*************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/** @type {import('next').NextConfig} */ const nextConfig = {\n    basePath: \"/agent\",\n    redirects: ()=>[\n            {\n                source: \"/\",\n                destination: \"/agent\",\n                permanent: false,\n                basePath: false\n            }\n        ],\n    images: {\n        remotePatterns: [\n            {\n                protocol: \"https\",\n                hostname: \"static.deepgram.com\",\n                port: \"\",\n                pathname: \"/examples/avatars/**\"\n            }\n        ]\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (nextConfig);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25leHQuY29uZmlnLm1qcyIsIm1hcHBpbmdzIjoiO0FBQUEsc0NBQXNDLEdBQ3RDLE1BQU1BLGFBQWE7SUFDakJDLFVBQVU7SUFDVkMsV0FBVyxJQUFNO1lBQUM7Z0JBQUVDLFFBQVE7Z0JBQUtDLGFBQWE7Z0JBQVVDLFdBQVc7Z0JBQU9KLFVBQVU7WUFBTTtTQUFFO0lBRTVGSyxRQUFRO1FBQ05DLGdCQUFnQjtZQUNkO2dCQUNFQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxNQUFNO2dCQUNOQyxVQUFVO1lBQ1o7U0FDRDtJQUNIO0FBQ0Y7QUFFQSwrREFBZVgsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9uZXh0LmNvbmZpZy5tanM/NzQzNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogQHR5cGUge2ltcG9ydCgnbmV4dCcpLk5leHRDb25maWd9ICovXG5jb25zdCBuZXh0Q29uZmlnID0ge1xuICBiYXNlUGF0aDogXCIvYWdlbnRcIixcbiAgcmVkaXJlY3RzOiAoKSA9PiBbeyBzb3VyY2U6IFwiL1wiLCBkZXN0aW5hdGlvbjogXCIvYWdlbnRcIiwgcGVybWFuZW50OiBmYWxzZSwgYmFzZVBhdGg6IGZhbHNlIH1dLFxuXG4gIGltYWdlczoge1xuICAgIHJlbW90ZVBhdHRlcm5zOiBbXG4gICAgICB7XG4gICAgICAgIHByb3RvY29sOiBcImh0dHBzXCIsXG4gICAgICAgIGhvc3RuYW1lOiBcInN0YXRpYy5kZWVwZ3JhbS5jb21cIixcbiAgICAgICAgcG9ydDogXCJcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2V4YW1wbGVzL2F2YXRhcnMvKipcIixcbiAgICAgIH0sXG4gICAgXSxcbiAgfSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IG5leHRDb25maWc7XG4iXSwibmFtZXMiOlsibmV4dENvbmZpZyIsImJhc2VQYXRoIiwicmVkaXJlY3RzIiwic291cmNlIiwiZGVzdGluYXRpb24iLCJwZXJtYW5lbnQiLCJpbWFnZXMiLCJyZW1vdGVQYXR0ZXJucyIsInByb3RvY29sIiwiaG9zdG5hbWUiLCJwb3J0IiwicGF0aG5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./next.config.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcomponents%2FAnimatedBackground.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FDeepgramContextProvider.js%22%2C%22ids%22%3A%5B%22DeepgramContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FMicrophoneContextProvider.js%22%2C%22ids%22%3A%5B%22MicrophoneContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fcontext%2FVoiceBotContextProvider.tsx%22%2C%22ids%22%3A%5B%22VoiceBotProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fira%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FABCFavorit-Bold.woff2%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-favorit%5C%22%2C%5C%22display%5C%22%3A%5C%22fallback%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22favorit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fhotovo%2FDocuments%2Faugment-projects%2Fdeepgram-voice-agent%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);