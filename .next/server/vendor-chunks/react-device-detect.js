"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-device-detect";
exports.ids = ["vendor-chunks/react-device-detect"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-device-detect/dist/lib.js":
/*!******************************************************!*\
  !*** ./node_modules/react-device-detect/dist/lib.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar React__default = _interopDefault(React);\n\nvar UAParser = __webpack_require__(/*! ua-parser-js/dist/ua-parser.min */ \"(ssr)/./node_modules/ua-parser-js/dist/ua-parser.min.js\");\n\nvar ClientUAInstance = new UAParser();\nvar browser = ClientUAInstance.getBrowser();\nvar cpu = ClientUAInstance.getCPU();\nvar device = ClientUAInstance.getDevice();\nvar engine = ClientUAInstance.getEngine();\nvar os = ClientUAInstance.getOS();\nvar ua = ClientUAInstance.getUA();\nvar setUa = function setUa(userAgentString) {\n  return ClientUAInstance.setUA(userAgentString);\n};\nvar parseUserAgent = function parseUserAgent(userAgent) {\n  if (!userAgent) {\n    console.error('No userAgent string was provided');\n    return;\n  }\n\n  var UserAgentInstance = new UAParser(userAgent);\n  return {\n    UA: UserAgentInstance,\n    browser: UserAgentInstance.getBrowser(),\n    cpu: UserAgentInstance.getCPU(),\n    device: UserAgentInstance.getDevice(),\n    engine: UserAgentInstance.getEngine(),\n    os: UserAgentInstance.getOS(),\n    ua: UserAgentInstance.getUA(),\n    setUserAgent: function setUserAgent(userAgentString) {\n      return UserAgentInstance.setUA(userAgentString);\n    }\n  };\n};\n\nvar UAHelper = /*#__PURE__*/Object.freeze({\n  ClientUAInstance: ClientUAInstance,\n  browser: browser,\n  cpu: cpu,\n  device: device,\n  engine: engine,\n  os: os,\n  ua: ua,\n  setUa: setUa,\n  parseUserAgent: parseUserAgent\n});\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar DeviceTypes = {\n  Mobile: 'mobile',\n  Tablet: 'tablet',\n  SmartTv: 'smarttv',\n  Console: 'console',\n  Wearable: 'wearable',\n  Embedded: 'embedded',\n  Browser: undefined\n};\nvar BrowserTypes = {\n  Chrome: 'Chrome',\n  Firefox: 'Firefox',\n  Opera: 'Opera',\n  Yandex: 'Yandex',\n  Safari: 'Safari',\n  InternetExplorer: 'Internet Explorer',\n  Edge: 'Edge',\n  Chromium: 'Chromium',\n  Ie: 'IE',\n  MobileSafari: 'Mobile Safari',\n  EdgeChromium: 'Edge Chromium',\n  MIUI: 'MIUI Browser',\n  SamsungBrowser: 'Samsung Browser'\n};\nvar OsTypes = {\n  IOS: 'iOS',\n  Android: 'Android',\n  WindowsPhone: 'Windows Phone',\n  Windows: 'Windows',\n  MAC_OS: 'Mac OS'\n};\nvar InitialDeviceTypes = {\n  isMobile: false,\n  isTablet: false,\n  isBrowser: false,\n  isSmartTV: false,\n  isConsole: false,\n  isWearable: false\n};\n\nvar checkDeviceType = function checkDeviceType(type) {\n  switch (type) {\n    case DeviceTypes.Mobile:\n      return {\n        isMobile: true\n      };\n\n    case DeviceTypes.Tablet:\n      return {\n        isTablet: true\n      };\n\n    case DeviceTypes.SmartTv:\n      return {\n        isSmartTV: true\n      };\n\n    case DeviceTypes.Console:\n      return {\n        isConsole: true\n      };\n\n    case DeviceTypes.Wearable:\n      return {\n        isWearable: true\n      };\n\n    case DeviceTypes.Browser:\n      return {\n        isBrowser: true\n      };\n\n    case DeviceTypes.Embedded:\n      return {\n        isEmbedded: true\n      };\n\n    default:\n      return InitialDeviceTypes;\n  }\n};\nvar setUserAgent = function setUserAgent(userAgent) {\n  return setUa(userAgent);\n};\nvar setDefaults = function setDefaults(p) {\n  var d = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'none';\n  return p ? p : d;\n};\nvar getNavigatorInstance = function getNavigatorInstance() {\n  if (typeof window !== 'undefined') {\n    if (window.navigator || navigator) {\n      return window.navigator || navigator;\n    }\n  }\n\n  return false;\n};\nvar isIOS13Check = function isIOS13Check(type) {\n  var nav = getNavigatorInstance();\n  return nav && nav.platform && (nav.platform.indexOf(type) !== -1 || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1 && !window.MSStream);\n};\n\nvar browserPayload = function browserPayload(isBrowser, browser, engine, os, ua) {\n  return {\n    isBrowser: isBrowser,\n    browserMajorVersion: setDefaults(browser.major),\n    browserFullVersion: setDefaults(browser.version),\n    browserName: setDefaults(browser.name),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar mobilePayload = function mobilePayload(type, device, os, ua) {\n  return _objectSpread2({}, type, {\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    os: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    ua: setDefaults(ua)\n  });\n};\nvar smartTvPayload = function smartTvPayload(isSmartTV, engine, os, ua) {\n  return {\n    isSmartTV: isSmartTV,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar consolePayload = function consolePayload(isConsole, engine, os, ua) {\n  return {\n    isConsole: isConsole,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar wearablePayload = function wearablePayload(isWearable, engine, os, ua) {\n  return {\n    isWearable: isWearable,\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\nvar embeddedPayload = function embeddedPayload(isEmbedded, device, engine, os, ua) {\n  return {\n    isEmbedded: isEmbedded,\n    vendor: setDefaults(device.vendor),\n    model: setDefaults(device.model),\n    engineName: setDefaults(engine.name),\n    engineVersion: setDefaults(engine.version),\n    osName: setDefaults(os.name),\n    osVersion: setDefaults(os.version),\n    userAgent: setDefaults(ua)\n  };\n};\n\nfunction deviceDetect(userAgent) {\n  var _ref = userAgent ? parseUserAgent(userAgent) : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      engine = _ref.engine,\n      os = _ref.os,\n      ua = _ref.ua;\n\n  var type = checkDeviceType(device.type);\n  var isBrowser = type.isBrowser,\n      isMobile = type.isMobile,\n      isTablet = type.isTablet,\n      isSmartTV = type.isSmartTV,\n      isConsole = type.isConsole,\n      isWearable = type.isWearable,\n      isEmbedded = type.isEmbedded;\n\n  if (isBrowser) {\n    return browserPayload(isBrowser, browser, engine, os, ua);\n  }\n\n  if (isSmartTV) {\n    return smartTvPayload(isSmartTV, engine, os, ua);\n  }\n\n  if (isConsole) {\n    return consolePayload(isConsole, engine, os, ua);\n  }\n\n  if (isMobile) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isTablet) {\n    return mobilePayload(type, device, os, ua);\n  }\n\n  if (isWearable) {\n    return wearablePayload(isWearable, engine, os, ua);\n  }\n\n  if (isEmbedded) {\n    return embeddedPayload(isEmbedded, device, engine, os, ua);\n  }\n}\n\nvar isMobileType = function isMobileType(_ref) {\n  var type = _ref.type;\n  return type === DeviceTypes.Mobile;\n};\nvar isTabletType = function isTabletType(_ref2) {\n  var type = _ref2.type;\n  return type === DeviceTypes.Tablet;\n};\nvar isMobileAndTabletType = function isMobileAndTabletType(_ref3) {\n  var type = _ref3.type;\n  return type === DeviceTypes.Mobile || type === DeviceTypes.Tablet;\n};\nvar isSmartTVType = function isSmartTVType(_ref4) {\n  var type = _ref4.type;\n  return type === DeviceTypes.SmartTv;\n};\nvar isBrowserType = function isBrowserType(_ref5) {\n  var type = _ref5.type;\n  return type === DeviceTypes.Browser;\n};\nvar isWearableType = function isWearableType(_ref6) {\n  var type = _ref6.type;\n  return type === DeviceTypes.Wearable;\n};\nvar isConsoleType = function isConsoleType(_ref7) {\n  var type = _ref7.type;\n  return type === DeviceTypes.Console;\n};\nvar isEmbeddedType = function isEmbeddedType(_ref8) {\n  var type = _ref8.type;\n  return type === DeviceTypes.Embedded;\n};\nvar getMobileVendor = function getMobileVendor(_ref9) {\n  var vendor = _ref9.vendor;\n  return setDefaults(vendor);\n};\nvar getMobileModel = function getMobileModel(_ref10) {\n  var model = _ref10.model;\n  return setDefaults(model);\n};\nvar getDeviceType = function getDeviceType(_ref11) {\n  var type = _ref11.type;\n  return setDefaults(type, 'browser');\n}; // os types\n\nvar isAndroidType = function isAndroidType(_ref12) {\n  var name = _ref12.name;\n  return name === OsTypes.Android;\n};\nvar isWindowsType = function isWindowsType(_ref13) {\n  var name = _ref13.name;\n  return name === OsTypes.Windows;\n};\nvar isMacOsType = function isMacOsType(_ref14) {\n  var name = _ref14.name;\n  return name === OsTypes.MAC_OS;\n};\nvar isWinPhoneType = function isWinPhoneType(_ref15) {\n  var name = _ref15.name;\n  return name === OsTypes.WindowsPhone;\n};\nvar isIOSType = function isIOSType(_ref16) {\n  var name = _ref16.name;\n  return name === OsTypes.IOS;\n};\nvar getOsVersion = function getOsVersion(_ref17) {\n  var version = _ref17.version;\n  return setDefaults(version);\n};\nvar getOsName = function getOsName(_ref18) {\n  var name = _ref18.name;\n  return setDefaults(name);\n}; // browser types\n\nvar isChromeType = function isChromeType(_ref19) {\n  var name = _ref19.name;\n  return name === BrowserTypes.Chrome;\n};\nvar isFirefoxType = function isFirefoxType(_ref20) {\n  var name = _ref20.name;\n  return name === BrowserTypes.Firefox;\n};\nvar isChromiumType = function isChromiumType(_ref21) {\n  var name = _ref21.name;\n  return name === BrowserTypes.Chromium;\n};\nvar isEdgeType = function isEdgeType(_ref22) {\n  var name = _ref22.name;\n  return name === BrowserTypes.Edge;\n};\nvar isYandexType = function isYandexType(_ref23) {\n  var name = _ref23.name;\n  return name === BrowserTypes.Yandex;\n};\nvar isSafariType = function isSafariType(_ref24) {\n  var name = _ref24.name;\n  return name === BrowserTypes.Safari || name === BrowserTypes.MobileSafari;\n};\nvar isMobileSafariType = function isMobileSafariType(_ref25) {\n  var name = _ref25.name;\n  return name === BrowserTypes.MobileSafari;\n};\nvar isOperaType = function isOperaType(_ref26) {\n  var name = _ref26.name;\n  return name === BrowserTypes.Opera;\n};\nvar isIEType = function isIEType(_ref27) {\n  var name = _ref27.name;\n  return name === BrowserTypes.InternetExplorer || name === BrowserTypes.Ie;\n};\nvar isMIUIType = function isMIUIType(_ref28) {\n  var name = _ref28.name;\n  return name === BrowserTypes.MIUI;\n};\nvar isSamsungBrowserType = function isSamsungBrowserType(_ref29) {\n  var name = _ref29.name;\n  return name === BrowserTypes.SamsungBrowser;\n};\nvar getBrowserFullVersion = function getBrowserFullVersion(_ref30) {\n  var version = _ref30.version;\n  return setDefaults(version);\n};\nvar getBrowserVersion = function getBrowserVersion(_ref31) {\n  var major = _ref31.major;\n  return setDefaults(major);\n};\nvar getBrowserName = function getBrowserName(_ref32) {\n  var name = _ref32.name;\n  return setDefaults(name);\n}; // engine types\n\nvar getEngineName = function getEngineName(_ref33) {\n  var name = _ref33.name;\n  return setDefaults(name);\n};\nvar getEngineVersion = function getEngineVersion(_ref34) {\n  var version = _ref34.version;\n  return setDefaults(version);\n};\nvar isElectronType = function isElectronType() {\n  var nav = getNavigatorInstance();\n  var ua = nav && nav.userAgent && nav.userAgent.toLowerCase();\n  return typeof ua === 'string' ? /electron/.test(ua) : false;\n};\nvar isEdgeChromiumType = function isEdgeChromiumType(ua) {\n  return typeof ua === 'string' && ua.indexOf('Edg/') !== -1;\n};\nvar getIOS13 = function getIOS13() {\n  var nav = getNavigatorInstance();\n  return nav && (/iPad|iPhone|iPod/.test(nav.platform) || nav.platform === 'MacIntel' && nav.maxTouchPoints > 1) && !window.MSStream;\n};\nvar getIPad13 = function getIPad13() {\n  return isIOS13Check('iPad');\n};\nvar getIphone13 = function getIphone13() {\n  return isIOS13Check('iPhone');\n};\nvar getIPod13 = function getIPod13() {\n  return isIOS13Check('iPod');\n};\nvar getUseragent = function getUseragent(userAg) {\n  return setDefaults(userAg);\n};\n\nfunction buildSelectorsObject(options) {\n  var _ref = options ? options : UAHelper,\n      device = _ref.device,\n      browser = _ref.browser,\n      os = _ref.os,\n      engine = _ref.engine,\n      ua = _ref.ua;\n\n  return {\n    isSmartTV: isSmartTVType(device),\n    isConsole: isConsoleType(device),\n    isWearable: isWearableType(device),\n    isEmbedded: isEmbeddedType(device),\n    isMobileSafari: isMobileSafariType(browser) || getIPad13(),\n    isChromium: isChromiumType(browser),\n    isMobile: isMobileAndTabletType(device) || getIPad13(),\n    isMobileOnly: isMobileType(device),\n    isTablet: isTabletType(device) || getIPad13(),\n    isBrowser: isBrowserType(device),\n    isDesktop: isBrowserType(device),\n    isAndroid: isAndroidType(os),\n    isWinPhone: isWinPhoneType(os),\n    isIOS: isIOSType(os) || getIPad13(),\n    isChrome: isChromeType(browser),\n    isFirefox: isFirefoxType(browser),\n    isSafari: isSafariType(browser),\n    isOpera: isOperaType(browser),\n    isIE: isIEType(browser),\n    osVersion: getOsVersion(os),\n    osName: getOsName(os),\n    fullBrowserVersion: getBrowserFullVersion(browser),\n    browserVersion: getBrowserVersion(browser),\n    browserName: getBrowserName(browser),\n    mobileVendor: getMobileVendor(device),\n    mobileModel: getMobileModel(device),\n    engineName: getEngineName(engine),\n    engineVersion: getEngineVersion(engine),\n    getUA: getUseragent(ua),\n    isEdge: isEdgeType(browser) || isEdgeChromiumType(ua),\n    isYandex: isYandexType(browser),\n    deviceType: getDeviceType(device),\n    isIOS13: getIOS13(),\n    isIPad13: getIPad13(),\n    isIPhone13: getIphone13(),\n    isIPod13: getIPod13(),\n    isElectron: isElectronType(),\n    isEdgeChromium: isEdgeChromiumType(ua),\n    isLegacyEdge: isEdgeType(browser) && !isEdgeChromiumType(ua),\n    isWindows: isWindowsType(os),\n    isMacOs: isMacOsType(os),\n    isMIUI: isMIUIType(browser),\n    isSamsungBrowser: isSamsungBrowserType(browser)\n  };\n}\n\nvar isSmartTV = isSmartTVType(device);\nvar isConsole = isConsoleType(device);\nvar isWearable = isWearableType(device);\nvar isEmbedded = isEmbeddedType(device);\nvar isMobileSafari = isMobileSafariType(browser) || getIPad13();\nvar isChromium = isChromiumType(browser);\nvar isMobile = isMobileAndTabletType(device) || getIPad13();\nvar isMobileOnly = isMobileType(device);\nvar isTablet = isTabletType(device) || getIPad13();\nvar isBrowser = isBrowserType(device);\nvar isDesktop = isBrowserType(device);\nvar isAndroid = isAndroidType(os);\nvar isWinPhone = isWinPhoneType(os);\nvar isIOS = isIOSType(os) || getIPad13();\nvar isChrome = isChromeType(browser);\nvar isFirefox = isFirefoxType(browser);\nvar isSafari = isSafariType(browser);\nvar isOpera = isOperaType(browser);\nvar isIE = isIEType(browser);\nvar osVersion = getOsVersion(os);\nvar osName = getOsName(os);\nvar fullBrowserVersion = getBrowserFullVersion(browser);\nvar browserVersion = getBrowserVersion(browser);\nvar browserName = getBrowserName(browser);\nvar mobileVendor = getMobileVendor(device);\nvar mobileModel = getMobileModel(device);\nvar engineName = getEngineName(engine);\nvar engineVersion = getEngineVersion(engine);\nvar getUA = getUseragent(ua);\nvar isEdge = isEdgeType(browser) || isEdgeChromiumType(ua);\nvar isYandex = isYandexType(browser);\nvar deviceType = getDeviceType(device);\nvar isIOS13 = getIOS13();\nvar isIPad13 = getIPad13();\nvar isIPhone13 = getIphone13();\nvar isIPod13 = getIPod13();\nvar isElectron = isElectronType();\nvar isEdgeChromium = isEdgeChromiumType(ua);\nvar isLegacyEdge = isEdgeType(browser) && !isEdgeChromiumType(ua);\nvar isWindows = isWindowsType(os);\nvar isMacOs = isMacOsType(os);\nvar isMIUI = isMIUIType(browser);\nvar isSamsungBrowser = isSamsungBrowserType(browser);\nvar getSelectorsByUserAgent = function getSelectorsByUserAgent(userAgent) {\n  if (!userAgent || typeof userAgent !== 'string') {\n    console.error('No valid user agent string was provided');\n    return;\n  }\n\n  var _UAHelper$parseUserAg = parseUserAgent(userAgent),\n      device = _UAHelper$parseUserAg.device,\n      browser = _UAHelper$parseUserAg.browser,\n      os = _UAHelper$parseUserAg.os,\n      engine = _UAHelper$parseUserAg.engine,\n      ua = _UAHelper$parseUserAg.ua;\n\n  return buildSelectorsObject({\n    device: device,\n    browser: browser,\n    os: os,\n    engine: engine,\n    ua: ua\n  });\n};\n\nvar AndroidView = function AndroidView(_ref) {\n  var renderWithFragment = _ref.renderWithFragment,\n      children = _ref.children,\n      props = _objectWithoutProperties(_ref, [\"renderWithFragment\", \"children\"]);\n\n  return isAndroid ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar BrowserView = function BrowserView(_ref2) {\n  var renderWithFragment = _ref2.renderWithFragment,\n      children = _ref2.children,\n      props = _objectWithoutProperties(_ref2, [\"renderWithFragment\", \"children\"]);\n\n  return isBrowser ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IEView = function IEView(_ref3) {\n  var renderWithFragment = _ref3.renderWithFragment,\n      children = _ref3.children,\n      props = _objectWithoutProperties(_ref3, [\"renderWithFragment\", \"children\"]);\n\n  return isIE ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar IOSView = function IOSView(_ref4) {\n  var renderWithFragment = _ref4.renderWithFragment,\n      children = _ref4.children,\n      props = _objectWithoutProperties(_ref4, [\"renderWithFragment\", \"children\"]);\n\n  return isIOS ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileView = function MobileView(_ref5) {\n  var renderWithFragment = _ref5.renderWithFragment,\n      children = _ref5.children,\n      props = _objectWithoutProperties(_ref5, [\"renderWithFragment\", \"children\"]);\n\n  return isMobile ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar TabletView = function TabletView(_ref6) {\n  var renderWithFragment = _ref6.renderWithFragment,\n      children = _ref6.children,\n      props = _objectWithoutProperties(_ref6, [\"renderWithFragment\", \"children\"]);\n\n  return isTablet ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WinPhoneView = function WinPhoneView(_ref7) {\n  var renderWithFragment = _ref7.renderWithFragment,\n      children = _ref7.children,\n      props = _objectWithoutProperties(_ref7, [\"renderWithFragment\", \"children\"]);\n\n  return isWinPhone ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar MobileOnlyView = function MobileOnlyView(_ref8) {\n  var renderWithFragment = _ref8.renderWithFragment,\n      children = _ref8.children,\n      viewClassName = _ref8.viewClassName,\n      style = _ref8.style,\n      props = _objectWithoutProperties(_ref8, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\"]);\n\n  return isMobileOnly ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar SmartTVView = function SmartTVView(_ref9) {\n  var renderWithFragment = _ref9.renderWithFragment,\n      children = _ref9.children,\n      props = _objectWithoutProperties(_ref9, [\"renderWithFragment\", \"children\"]);\n\n  return isSmartTV ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar ConsoleView = function ConsoleView(_ref10) {\n  var renderWithFragment = _ref10.renderWithFragment,\n      children = _ref10.children,\n      props = _objectWithoutProperties(_ref10, [\"renderWithFragment\", \"children\"]);\n\n  return isConsole ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar WearableView = function WearableView(_ref11) {\n  var renderWithFragment = _ref11.renderWithFragment,\n      children = _ref11.children,\n      props = _objectWithoutProperties(_ref11, [\"renderWithFragment\", \"children\"]);\n\n  return isWearable ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\nvar CustomView = function CustomView(_ref12) {\n  var renderWithFragment = _ref12.renderWithFragment,\n      children = _ref12.children,\n      viewClassName = _ref12.viewClassName,\n      style = _ref12.style,\n      condition = _ref12.condition,\n      props = _objectWithoutProperties(_ref12, [\"renderWithFragment\", \"children\", \"viewClassName\", \"style\", \"condition\"]);\n\n  return condition ? renderWithFragment ? React__default.createElement(React.Fragment, null, children) : React__default.createElement(\"div\", props, children) : null;\n};\n\nfunction withOrientationChange(WrappedComponent) {\n  return /*#__PURE__*/function (_React$Component) {\n    _inherits(_class, _React$Component);\n\n    function _class(props) {\n      var _this;\n\n      _classCallCheck(this, _class);\n\n      _this = _possibleConstructorReturn(this, _getPrototypeOf(_class).call(this, props));\n      _this.isEventListenerAdded = false;\n      _this.handleOrientationChange = _this.handleOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onOrientationChange = _this.onOrientationChange.bind(_assertThisInitialized(_this));\n      _this.onPageLoad = _this.onPageLoad.bind(_assertThisInitialized(_this));\n      _this.state = {\n        isLandscape: false,\n        isPortrait: false\n      };\n      return _this;\n    }\n\n    _createClass(_class, [{\n      key: \"handleOrientationChange\",\n      value: function handleOrientationChange() {\n        if (!this.isEventListenerAdded) {\n          this.isEventListenerAdded = true;\n        }\n\n        var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n        this.setState({\n          isPortrait: orientation === 0,\n          isLandscape: orientation === 90\n        });\n      }\n    }, {\n      key: \"onOrientationChange\",\n      value: function onOrientationChange() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"onPageLoad\",\n      value: function onPageLoad() {\n        this.handleOrientationChange();\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n          if (!this.isEventListenerAdded) {\n            this.handleOrientationChange();\n            window.addEventListener(\"load\", this.onPageLoad, false);\n          } else {\n            window.removeEventListener(\"load\", this.onPageLoad, false);\n          }\n\n          window.addEventListener(\"resize\", this.onOrientationChange, false);\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        window.removeEventListener(\"resize\", this.onOrientationChange, false);\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        return React__default.createElement(WrappedComponent, _extends({}, this.props, {\n          isLandscape: this.state.isLandscape,\n          isPortrait: this.state.isPortrait\n        }));\n      }\n    }]);\n\n    return _class;\n  }(React__default.Component);\n}\n\nfunction useMobileOrientation() {\n  var _useState = React.useState(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    return {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n\n  var handleOrientationChange = React.useCallback(function () {\n    var orientation = window.innerWidth > window.innerHeight ? 90 : 0;\n    var next = {\n      isPortrait: orientation === 0,\n      isLandscape: orientation === 90,\n      orientation: orientation === 0 ? 'portrait' : 'landscape'\n    };\n    state.orientation !== next.orientation && setState(next);\n  }, [state.orientation]);\n  React.useEffect(function () {\n    if ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) !== undefined && isMobile) {\n      handleOrientationChange();\n      window.addEventListener(\"load\", handleOrientationChange, false);\n      window.addEventListener(\"resize\", handleOrientationChange, false);\n    }\n\n    return function () {\n      window.removeEventListener(\"resize\", handleOrientationChange, false);\n      window.removeEventListener(\"load\", handleOrientationChange, false);\n    };\n  }, [handleOrientationChange]);\n  return state;\n}\n\nfunction useDeviceData(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  return parseUserAgent(hookUserAgent);\n}\n\nfunction useDeviceSelectors(userAgent) {\n  var hookUserAgent = userAgent ? userAgent : window.navigator.userAgent;\n  var deviceData = useDeviceData(hookUserAgent);\n  var selectors = buildSelectorsObject(deviceData);\n  return [selectors, deviceData];\n}\n\nexports.AndroidView = AndroidView;\nexports.BrowserTypes = BrowserTypes;\nexports.BrowserView = BrowserView;\nexports.ConsoleView = ConsoleView;\nexports.CustomView = CustomView;\nexports.IEView = IEView;\nexports.IOSView = IOSView;\nexports.MobileOnlyView = MobileOnlyView;\nexports.MobileView = MobileView;\nexports.OsTypes = OsTypes;\nexports.SmartTVView = SmartTVView;\nexports.TabletView = TabletView;\nexports.WearableView = WearableView;\nexports.WinPhoneView = WinPhoneView;\nexports.browserName = browserName;\nexports.browserVersion = browserVersion;\nexports.deviceDetect = deviceDetect;\nexports.deviceType = deviceType;\nexports.engineName = engineName;\nexports.engineVersion = engineVersion;\nexports.fullBrowserVersion = fullBrowserVersion;\nexports.getSelectorsByUserAgent = getSelectorsByUserAgent;\nexports.getUA = getUA;\nexports.isAndroid = isAndroid;\nexports.isBrowser = isBrowser;\nexports.isChrome = isChrome;\nexports.isChromium = isChromium;\nexports.isConsole = isConsole;\nexports.isDesktop = isDesktop;\nexports.isEdge = isEdge;\nexports.isEdgeChromium = isEdgeChromium;\nexports.isElectron = isElectron;\nexports.isEmbedded = isEmbedded;\nexports.isFirefox = isFirefox;\nexports.isIE = isIE;\nexports.isIOS = isIOS;\nexports.isIOS13 = isIOS13;\nexports.isIPad13 = isIPad13;\nexports.isIPhone13 = isIPhone13;\nexports.isIPod13 = isIPod13;\nexports.isLegacyEdge = isLegacyEdge;\nexports.isMIUI = isMIUI;\nexports.isMacOs = isMacOs;\nexports.isMobile = isMobile;\nexports.isMobileOnly = isMobileOnly;\nexports.isMobileSafari = isMobileSafari;\nexports.isOpera = isOpera;\nexports.isSafari = isSafari;\nexports.isSamsungBrowser = isSamsungBrowser;\nexports.isSmartTV = isSmartTV;\nexports.isTablet = isTablet;\nexports.isWearable = isWearable;\nexports.isWinPhone = isWinPhone;\nexports.isWindows = isWindows;\nexports.isYandex = isYandex;\nexports.mobileModel = mobileModel;\nexports.mobileVendor = mobileVendor;\nexports.osName = osName;\nexports.osVersion = osVersion;\nexports.parseUserAgent = parseUserAgent;\nexports.setUserAgent = setUserAgent;\nexports.useDeviceData = useDeviceData;\nexports.useDeviceSelectors = useDeviceSelectors;\nexports.useMobileOrientation = useMobileOrientation;\nexports.withOrientationChange = withOrientationChange;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-device-detect/dist/lib.js\n");

/***/ })

};
;