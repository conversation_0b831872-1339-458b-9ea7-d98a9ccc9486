{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"mn": {"title": "Match Name", "description": "After Effect's Match Name. Used for expressions.", "type": "string"}, "nm": {"title": "Name", "description": "After Effect's Name. Used for expressions.", "type": "string"}, "d": {"title": "Direction", "description": "After Effect's Direction. Direction how the shape is drawn. Used for trim path for example.", "type": "number"}, "ty": {"title": "Type", "description": "Shape content type.", "type": "string", "const": "sr"}, "p": {"title": "Position", "description": "Star's position", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "object"}, "ir": {"title": "Inner Radius", "description": "Star's inner radius. (Star only)", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "is": {"title": "Inner Roundness", "description": "<PERSON>'s inner roundness. (Star only)", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "or": {"title": "Outer Radius", "description": "Star's outer radius.", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "os": {"title": "Outer Roundness", "description": "Star's outer roundness.", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "r": {"title": "Rotation", "description": "Star's rotation.", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "pt": {"title": "Points", "description": "Star's number of points.", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, "sy": {"title": "Star Type", "description": "Star's type. Polygon or Star.", "type": "object", "oneOf": [{"standsFor": "Star", "value": 1}, {"standsFor": "Polygon", "value": 2}]}}}