{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"ix": {"title": "Effect Index", "description": "Effect Index. Used for expressions. NOT USED. EQUALS SLIDER.", "type": "number"}, "mn": {"title": "Match Name", "description": "After Effect's Match Name. Used for expressions.", "type": "string"}, "nm": {"title": "Name", "description": "After Effect's Name. Used for expressions.", "type": "string"}, "ty": {"title": "Type", "description": "Effect type.", "type": "number", "const": 1}, "v": {"title": "Value", "description": "Effect value.", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}}}