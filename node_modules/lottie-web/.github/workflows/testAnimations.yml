# This workflow will do a clean install of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  pull_request

jobs:

  create_snapshots:
  
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js 16.x
      uses: actions/cache@v2
      with:
        path: ~/.npm
        key: ${{ runner.os }}-${{ hashFiles('**/lockfiles') }}
    - run: npm install
    - run: npm run test:create
    - run: npm run build
    - run: npm run test:compare