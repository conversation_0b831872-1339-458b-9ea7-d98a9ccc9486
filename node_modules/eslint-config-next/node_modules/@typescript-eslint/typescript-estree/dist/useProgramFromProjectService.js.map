{"version": 3, "file": "useProgramFromProjectService.js", "sourceRoot": "", "sources": ["../src/useProgramFromProjectService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,yCAAsC;AACtC,gDAAwB;AAExB,gFAA6E;AAK7E,MAAM,GAAG,GAAG,IAAA,eAAK,EACf,kEAAkE,CACnE,CAAC;AAEF,SAAgB,4BAA4B,CAC1C,EAAE,2BAA2B,EAAE,OAAO,EAA0B,EAChE,aAA6C,EAC7C,sBAA+B;IAE/B,iFAAiF;IACjF,yEAAyE;IACzE,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC5D,GAAG,CACD,0DAA0D,EAC1D,aAAa,CAAC,QAAQ,EACtB,gBAAgB,CACjB,CAAC;IAEF,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CACnC,gBAAgB,EAChB,aAAa,CAAC,YAAY;IAC1B,gBAAgB,CAAC,SAAS,EAC1B,aAAa,CAAC,eAAe,CAC9B,CAAC;IAEF,GAAG,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;IAE/C,IAAI,sBAAsB,EAAE,CAAC;QAC3B,GAAG,CACD,+EAA+E,EAC/E,2BAA2B,CAC5B,CAAC;QACF,MAAM,2BAA2B,GAAG,iBAAiB,CACnD,aAAa,CAAC,QAAQ,EACtB,2BAA2B,CAC5B,CAAC;QAEF,GAAG,CACD,4DAA4D,EAC5D,2BAA2B,EAC3B,MAAM,CAAC,cAAc,CACtB,CAAC;QAEF,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,IAAI,2BAA2B,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CACb,GAAG,aAAa,CAAC,QAAQ,gJAAgJ,CAC1K,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CACb,GAAG,aAAa,CAAC,QAAQ,0IAA0I,CACpK,CAAC;QACJ,CAAC;IACH,CAAC;IACD,GAAG,CAAC,iDAAiD,EAAE,gBAAgB,CAAC,CAAC;IAEzE,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAC3D,6DAA6D;IAC7D,MAAM,OAAO,GAAG,OAAO;SACpB,wBAAwB,CAAC,UAAW,CAAC,QAAQ,EAAE,IAAI,CAAE;SACrD,kBAAkB,CAAC,sBAAsB,CAAC,IAAI,CAAC;SAC/C,UAAU,EAAE,CAAC;IAChB,4DAA4D;IAE5D,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,GAAG,CAAC,gDAAgD,EAAE,gBAAgB,CAAC,CAAC;QACxE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,GAAG,CAAC,uCAAuC,EAAE,gBAAgB,CAAC,CAAC;IAE/D,OAAO,IAAA,2CAAoB,EAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEtD,SAAS,UAAU,CAAC,QAAgB;QAClC,OAAO,cAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC9B,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AA3ED,oEA2EC;AAED,SAAS,iBAAiB,CACxB,QAAgB,EAChB,2BAAiD;IAEjD,OAAO,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CACnD,IAAA,qBAAS,EAAC,QAAQ,EAAE,OAAO,CAAC,CAC7B,CAAC;AACJ,CAAC"}