{"version": 3, "file": "resolveProjectList.js", "sourceRoot": "", "sources": ["../../src/parseSettings/resolveProjectList.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,mCAA0C;AAC1C,sDAA6B;AAG7B,qDAIkC;AAElC,mDAGyB;AAEzB,MAAM,GAAG,GAAG,IAAA,eAAK,EACf,6EAA6E,CAC9E,CAAC;AAEF,IAAI,gBAAgB,GAClB,IAAI,CAAC;AAEP,SAAgB,cAAc;IAC5B,gBAAgB,EAAE,KAAK,EAAE,CAAC;AAC5B,CAAC;AAFD,wCAEC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAChC,OAME;IAEF,MAAM,iBAAiB,GAAa,EAAE,CAAC;IAEvC,2CAA2C;IAC3C,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QAC5B,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACtC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,uBAAuB,GAAG,CAC9B,OAAO,CAAC,uBAAuB,IAAI,CAAC,oBAAoB,CAAC,CAC1D;SACE,MAAM,CAAW,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;QAChC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC;QACN,qCAAqC;SACpC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC;IAEnE,MAAM,QAAQ,GAAG,OAAO,CAAC;QACvB,OAAO,EAAE,iBAAiB;QAC1B,uBAAuB;QACvB,eAAe,EAAE,OAAO,CAAC,eAAe;KACzC,CAAC,CAAC;IACH,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gFAAgF;QAChF,8EAA8E;QAC9E,8EAA8E;QAC9E,8EAA8E;QAC9E,gBAAgB,GAAG,IAAI,6BAAa,CAClC,OAAO,CAAC,SAAS;YACf,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI;gBAC3B,uDAAuC,CAC5C,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,qCAAqC;IACrC,MAAM,eAAe,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAA,iBAAM,EAAC,OAAO,CAAC,CAAC,CAAC;IAC9E,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAA,iBAAM,EAAC,OAAO,CAAC,CAAC,CAAC;IAE1E,MAAM,2BAA2B,GAAG,IAAI,GAAG,CACzC,eAAe;SACZ,MAAM,CACL,YAAY,CAAC,MAAM,KAAK,CAAC;QACvB,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,IAAA,aAAQ,EAAC,CAAC,GAAG,YAAY,EAAE,GAAG,uBAAuB,CAAC,EAAE;YACtD,GAAG,EAAE,OAAO,CAAC,eAAe;SAC7B,CAAC,CACP;SACA,GAAG,CAAC,OAAO,CAAC,EAAE,CACb,IAAA,6BAAoB,EAClB,IAAA,2BAAkB,EAAC,OAAO,EAAE,OAAO,CAAC,eAAe,CAAC,CACrD,CACF,CACJ,CAAC;IAEF,GAAG,CACD,gEAAgE,EAChE,2BAA2B,CAC5B,CAAC;IAEF,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC5D,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC5C,OAAO,WAAW,CAAC;AACrB,CAAC;AAvFD,gDAuFC;AAED,SAAS,OAAO,CAAC,EACf,OAAO,EACP,uBAAuB,EACvB,eAAe,GAKf;IACA,+CAA+C;IAC/C,MAAM,UAAU,GAAG;QACjB,eAAe;QACf,kEAAkE;QAClE,OAAO;QACP,6CAA6C;QAC7C,uBAAuB,EAAE,CAAC,GAAG,uBAAuB,CAAC,CAAC,IAAI,EAAE;KAC7D,CAAC;IAEF,OAAO,IAAA,mBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAChD,CAAC;AAED;;;GAGG;AACH,SAAgB,wBAAwB;IACtC,gBAAgB,EAAE,KAAK,EAAE,CAAC;IAC1B,gBAAgB,GAAG,IAAI,CAAC;AAC1B,CAAC;AAHD,4DAGC"}